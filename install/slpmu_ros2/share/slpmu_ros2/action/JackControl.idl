// generated from rosidl_adapter/resource/action.idl.em
// with input from slpmu_ros2/action/JackControl.action
// generated code does not contain a copyright notice


module slpmu_ros2 {
  module action {
    @verbatim (language="comment", text=
      "Jack Control Action Definition" "\n"
      "This action provides control over the lifting jack system")
    struct JackControl_Goal {
      @verbatim (language="comment", text=
        "Goal - The requested jack operation" "\n"
        "One of: \"detect_base\", \"lift_up\", \"lift_down\", \"stop\", \"clear_alarm\"")
      string command;

      @verbatim (language="comment", text=
        "Target position for lift operations (optional, used for lift_up/lift_down)")
      uint32 target_position;

      @verbatim (language="comment", text=
        "Speed for lift operations (optional, RPM)")
      uint32 speed;
    };
    struct JackControl_Result {
      @verbatim (language="comment", text=
        "Result - The final result of the jack operation" "\n"
        "Whether the operation completed successfully")
      boolean success;

      @verbatim (language="comment", text=
        "Human-readable result message")
      string message;

      @verbatim (language="comment", text=
        "Final position after operation")
      uint32 final_position;

      @verbatim (language="comment", text=
        "Final status word from jack controller")
      uint16 final_status;

      @verbatim (language="comment", text=
        "Any alarm codes present")
      uint16 alarm_code;
    };
    struct JackControl_Feedback {
      @verbatim (language="comment", text=
        "Feedback - Ongoing feedback during the operation" "\n"
        "Current stage: \"init\", \"detecting_base\", \"base_stop\", \"lifting_up\", \"lifting_down\", \"top_stop\", \"middle_stop\"")
      string current_stage;

      @verbatim (language="comment", text=
        "Current position")
      uint32 current_position;

      @verbatim (language="comment", text=
        "Current status word")
      uint16 current_status;

      @verbatim (language="comment", text=
        "Current alarm code")
      uint16 current_alarm;

      @verbatim (language="comment", text=
        "Progress percentage (0.0 to 100.0)")
      float progress;
    };
  };
};
