// generated from rosidl_adapter/resource/msg.idl.em
// with input from slpmu_ros2/msg/JackStatus.msg
// generated code does not contain a copyright notice

#include "std_msgs/msg/Header.idl"

module slpmu_ros2 {
  module msg {
    @verbatim (language="comment", text=
      "Jack Status Message" "\n"
      "This message contains the current status information of the jack control system")
    struct JackStatus {
      std_msgs::msg::Header header;

      @verbatim (language="comment", text=
        "Current operational stage" "\n"
        "Current stage: \"init\", \"detecting_base\", \"base_stop\", \"lifting_up\", \"lifting_down\", \"top_stop\", \"middle_stop\"")
      string current_stage;

      @verbatim (language="comment", text=
        "Position and status information" "\n"
        "Current position in encoder counts")
      int32 current_position;

      @verbatim (language="comment", text=
        "Current status word from jack controller")
      uint16 current_status;

      @verbatim (language="comment", text=
        "Current alarm code from jack controller")
      uint16 current_alarm;
    };
  };
};
