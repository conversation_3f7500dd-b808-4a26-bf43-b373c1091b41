# generated from rosidl_generator_py/resource/_idl.py.em
# with input from slpmu_ros2:msg/JackStatus.idl
# generated code does not contain a copyright notice


# Import statements for member types

import builtins  # noqa: E402, I100

import rosidl_parser.definition  # noqa: E402, I100


class Metaclass_JackStatus(type):
    """Metaclass of message 'JackStatus'."""

    _CREATE_ROS_MESSAGE = None
    _CONVERT_FROM_PY = None
    _CONVERT_TO_PY = None
    _DESTROY_ROS_MESSAGE = None
    _TYPE_SUPPORT = None

    __constants = {
    }

    @classmethod
    def __import_type_support__(cls):
        try:
            from rosidl_generator_py import import_type_support
            module = import_type_support('slpmu_ros2')
        except ImportError:
            import logging
            import traceback
            logger = logging.getLogger(
                'slpmu_ros2.msg.JackStatus')
            logger.debug(
                'Failed to import needed modules for type support:\n' +
                traceback.format_exc())
        else:
            cls._CREATE_ROS_MESSAGE = module.create_ros_message_msg__msg__jack_status
            cls._CONVERT_FROM_PY = module.convert_from_py_msg__msg__jack_status
            cls._CONVERT_TO_PY = module.convert_to_py_msg__msg__jack_status
            cls._TYPE_SUPPORT = module.type_support_msg__msg__jack_status
            cls._DESTROY_ROS_MESSAGE = module.destroy_ros_message_msg__msg__jack_status

            from std_msgs.msg import Header
            if Header.__class__._TYPE_SUPPORT is None:
                Header.__class__.__import_type_support__()

    @classmethod
    def __prepare__(cls, name, bases, **kwargs):
        # list constant names here so that they appear in the help text of
        # the message class under "Data and other attributes defined here:"
        # as well as populate each message instance
        return {
        }


class JackStatus(metaclass=Metaclass_JackStatus):
    """Message class 'JackStatus'."""

    __slots__ = [
        '_header',
        '_current_stage',
        '_current_position',
        '_current_status',
        '_current_alarm',
    ]

    _fields_and_field_types = {
        'header': 'std_msgs/Header',
        'current_stage': 'string',
        'current_position': 'int32',
        'current_status': 'uint16',
        'current_alarm': 'uint16',
    }

    SLOT_TYPES = (
        rosidl_parser.definition.NamespacedType(['std_msgs', 'msg'], 'Header'),  # noqa: E501
        rosidl_parser.definition.UnboundedString(),  # noqa: E501
        rosidl_parser.definition.BasicType('int32'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint16'),  # noqa: E501
        rosidl_parser.definition.BasicType('uint16'),  # noqa: E501
    )

    def __init__(self, **kwargs):
        assert all('_' + key in self.__slots__ for key in kwargs.keys()), \
            'Invalid arguments passed to constructor: %s' % \
            ', '.join(sorted(k for k in kwargs.keys() if '_' + k not in self.__slots__))
        from std_msgs.msg import Header
        self.header = kwargs.get('header', Header())
        self.current_stage = kwargs.get('current_stage', str())
        self.current_position = kwargs.get('current_position', int())
        self.current_status = kwargs.get('current_status', int())
        self.current_alarm = kwargs.get('current_alarm', int())

    def __repr__(self):
        typename = self.__class__.__module__.split('.')
        typename.pop()
        typename.append(self.__class__.__name__)
        args = []
        for s, t in zip(self.__slots__, self.SLOT_TYPES):
            field = getattr(self, s)
            fieldstr = repr(field)
            # We use Python array type for fields that can be directly stored
            # in them, and "normal" sequences for everything else.  If it is
            # a type that we store in an array, strip off the 'array' portion.
            if (
                isinstance(t, rosidl_parser.definition.AbstractSequence) and
                isinstance(t.value_type, rosidl_parser.definition.BasicType) and
                t.value_type.typename in ['float', 'double', 'int8', 'uint8', 'int16', 'uint16', 'int32', 'uint32', 'int64', 'uint64']
            ):
                if len(field) == 0:
                    fieldstr = '[]'
                else:
                    assert fieldstr.startswith('array(')
                    prefix = "array('X', "
                    suffix = ')'
                    fieldstr = fieldstr[len(prefix):-len(suffix)]
            args.append(s[1:] + '=' + fieldstr)
        return '%s(%s)' % ('.'.join(typename), ', '.join(args))

    def __eq__(self, other):
        if not isinstance(other, self.__class__):
            return False
        if self.header != other.header:
            return False
        if self.current_stage != other.current_stage:
            return False
        if self.current_position != other.current_position:
            return False
        if self.current_status != other.current_status:
            return False
        if self.current_alarm != other.current_alarm:
            return False
        return True

    @classmethod
    def get_fields_and_field_types(cls):
        from copy import copy
        return copy(cls._fields_and_field_types)

    @builtins.property
    def header(self):
        """Message field 'header'."""
        return self._header

    @header.setter
    def header(self, value):
        if __debug__:
            from std_msgs.msg import Header
            assert \
                isinstance(value, Header), \
                "The 'header' field must be a sub message of type 'Header'"
        self._header = value

    @builtins.property
    def current_stage(self):
        """Message field 'current_stage'."""
        return self._current_stage

    @current_stage.setter
    def current_stage(self, value):
        if __debug__:
            assert \
                isinstance(value, str), \
                "The 'current_stage' field must be of type 'str'"
        self._current_stage = value

    @builtins.property
    def current_position(self):
        """Message field 'current_position'."""
        return self._current_position

    @current_position.setter
    def current_position(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'current_position' field must be of type 'int'"
            assert value >= -2147483648 and value < 2147483648, \
                "The 'current_position' field must be an integer in [-2147483648, 2147483647]"
        self._current_position = value

    @builtins.property
    def current_status(self):
        """Message field 'current_status'."""
        return self._current_status

    @current_status.setter
    def current_status(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'current_status' field must be of type 'int'"
            assert value >= 0 and value < 65536, \
                "The 'current_status' field must be an unsigned integer in [0, 65535]"
        self._current_status = value

    @builtins.property
    def current_alarm(self):
        """Message field 'current_alarm'."""
        return self._current_alarm

    @current_alarm.setter
    def current_alarm(self, value):
        if __debug__:
            assert \
                isinstance(value, int), \
                "The 'current_alarm' field must be of type 'int'"
            assert value >= 0 and value < 65536, \
                "The 'current_alarm' field must be an unsigned integer in [0, 65535]"
        self._current_alarm = value
