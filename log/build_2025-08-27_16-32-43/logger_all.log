[0.149s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build']
[0.149s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=8, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=None, packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, mixin_files=None, mixin=None, verb_parser=<colcon_mixin.mixin.mixin_argument.MixinArgumentDecorator object at 0x7fa32018a0>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x7fa33b67d0>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x7fa33b67d0>>, mixin_verb=('build',))
[0.366s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.366s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.366s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.366s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.366s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.366s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.366s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/yuan/amr_ws'
[0.366s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.367s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.367s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.367s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.367s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.367s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.367s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.367s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.367s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.383s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.383s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.383s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.383s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.383s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.383s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.383s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.383s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.384s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.384s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.384s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.384s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.384s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.384s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.384s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[0.384s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[0.384s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[0.384s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[0.384s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[0.385s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[0.385s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[0.385s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[0.385s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[0.385s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[0.385s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[0.385s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[0.385s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[0.385s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[0.385s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu) by extensions ['ignore', 'ignore_ament_install']
[0.385s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu) by extension 'ignore'
[0.385s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu) by extension 'ignore_ament_install'
[0.385s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu) by extensions ['colcon_pkg']
[0.385s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu) by extension 'colcon_pkg'
[0.385s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu) by extensions ['colcon_meta']
[0.385s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu) by extension 'colcon_meta'
[0.385s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu) by extensions ['ros']
[0.385s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu) by extension 'ros'
[0.385s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu) by extensions ['cmake', 'python']
[0.386s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu) by extension 'cmake'
[0.386s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu) by extension 'python'
[0.386s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu) by extensions ['python_setup_py']
[0.386s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu) by extension 'python_setup_py'
[0.386s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build) by extensions ['ignore', 'ignore_ament_install']
[0.386s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build) by extension 'ignore'
[0.386s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build) by extension 'ignore_ament_install'
[0.386s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build) by extensions ['colcon_pkg']
[0.386s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build) by extension 'colcon_pkg'
[0.386s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build) by extensions ['colcon_meta']
[0.386s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build) by extension 'colcon_meta'
[0.386s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build) by extensions ['ros']
[0.386s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build) by extension 'ros'
[0.386s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build) by extensions ['cmake', 'python']
[0.386s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build) by extension 'cmake'
[0.386s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build) by extension 'python'
[0.386s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build) by extensions ['python_setup_py']
[0.386s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build) by extension 'python_setup_py'
[0.387s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan) by extensions ['ignore', 'ignore_ament_install']
[0.387s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan) by extension 'ignore'
[0.387s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan) by extension 'ignore_ament_install'
[0.387s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan) by extensions ['colcon_pkg']
[0.387s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan) by extension 'colcon_pkg'
[0.387s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan) by extensions ['colcon_meta']
[0.387s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan) by extension 'colcon_meta'
[0.387s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan) by extensions ['ros']
[0.387s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan) by extension 'ros'
[0.387s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan) by extensions ['cmake', 'python']
[0.387s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan) by extension 'cmake'
[0.387s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan) by extension 'python'
[0.387s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan) by extensions ['python_setup_py']
[0.387s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan) by extension 'python_setup_py'
[0.387s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles) by extensions ['ignore', 'ignore_ament_install']
[0.388s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles) by extension 'ignore'
[0.388s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles) by extension 'ignore_ament_install'
[0.388s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles) by extensions ['colcon_pkg']
[0.388s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles) by extension 'colcon_pkg'
[0.388s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles) by extensions ['colcon_meta']
[0.388s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles) by extension 'colcon_meta'
[0.388s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles) by extensions ['ros']
[0.388s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles) by extension 'ros'
[0.388s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles) by extensions ['cmake', 'python']
[0.388s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles) by extension 'cmake'
[0.388s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles) by extension 'python'
[0.388s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles) by extensions ['python_setup_py']
[0.388s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles) by extension 'python_setup_py'
[0.388s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/3.22.1) by extensions ['ignore', 'ignore_ament_install']
[0.388s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/3.22.1) by extension 'ignore'
[0.388s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/3.22.1) by extension 'ignore_ament_install'
[0.388s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/3.22.1) by extensions ['colcon_pkg']
[0.388s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/3.22.1) by extension 'colcon_pkg'
[0.389s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/3.22.1) by extensions ['colcon_meta']
[0.389s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/3.22.1) by extension 'colcon_meta'
[0.389s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/3.22.1) by extensions ['ros']
[0.389s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/3.22.1) by extension 'ros'
[0.389s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/3.22.1) by extensions ['cmake', 'python']
[0.389s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/3.22.1) by extension 'cmake'
[0.389s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/3.22.1) by extension 'python'
[0.389s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/3.22.1) by extensions ['python_setup_py']
[0.389s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/3.22.1) by extension 'python_setup_py'
[0.389s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/3.22.1/CompilerIdC) by extensions ['ignore', 'ignore_ament_install']
[0.389s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/3.22.1/CompilerIdC) by extension 'ignore'
[0.389s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/3.22.1/CompilerIdC) by extension 'ignore_ament_install'
[0.389s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/3.22.1/CompilerIdC) by extensions ['colcon_pkg']
[0.389s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/3.22.1/CompilerIdC) by extension 'colcon_pkg'
[0.389s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/3.22.1/CompilerIdC) by extensions ['colcon_meta']
[0.389s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/3.22.1/CompilerIdC) by extension 'colcon_meta'
[0.389s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/3.22.1/CompilerIdC) by extensions ['ros']
[0.390s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/3.22.1/CompilerIdC) by extension 'ros'
[0.390s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/3.22.1/CompilerIdC) by extensions ['cmake', 'python']
[0.390s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/3.22.1/CompilerIdC) by extension 'cmake'
[0.390s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/3.22.1/CompilerIdC) by extension 'python'
[0.390s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/3.22.1/CompilerIdC) by extensions ['python_setup_py']
[0.390s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/3.22.1/CompilerIdC) by extension 'python_setup_py'
[0.390s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/3.22.1/CompilerIdC/tmp) by extensions ['ignore', 'ignore_ament_install']
[0.390s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/3.22.1/CompilerIdC/tmp) by extension 'ignore'
[0.390s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/3.22.1/CompilerIdC/tmp) by extension 'ignore_ament_install'
[0.390s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/3.22.1/CompilerIdC/tmp) by extensions ['colcon_pkg']
[0.390s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/3.22.1/CompilerIdC/tmp) by extension 'colcon_pkg'
[0.390s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/3.22.1/CompilerIdC/tmp) by extensions ['colcon_meta']
[0.390s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/3.22.1/CompilerIdC/tmp) by extension 'colcon_meta'
[0.390s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/3.22.1/CompilerIdC/tmp) by extensions ['ros']
[0.390s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/3.22.1/CompilerIdC/tmp) by extension 'ros'
[0.390s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/3.22.1/CompilerIdC/tmp) by extensions ['cmake', 'python']
[0.390s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/3.22.1/CompilerIdC/tmp) by extension 'cmake'
[0.391s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/3.22.1/CompilerIdC/tmp) by extension 'python'
[0.391s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/3.22.1/CompilerIdC/tmp) by extensions ['python_setup_py']
[0.391s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/3.22.1/CompilerIdC/tmp) by extension 'python_setup_py'
[0.391s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/3.22.1/CompilerIdCXX) by extensions ['ignore', 'ignore_ament_install']
[0.391s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/3.22.1/CompilerIdCXX) by extension 'ignore'
[0.391s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/3.22.1/CompilerIdCXX) by extension 'ignore_ament_install'
[0.391s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/3.22.1/CompilerIdCXX) by extensions ['colcon_pkg']
[0.391s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/3.22.1/CompilerIdCXX) by extension 'colcon_pkg'
[0.391s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/3.22.1/CompilerIdCXX) by extensions ['colcon_meta']
[0.391s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/3.22.1/CompilerIdCXX) by extension 'colcon_meta'
[0.391s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/3.22.1/CompilerIdCXX) by extensions ['ros']
[0.391s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/3.22.1/CompilerIdCXX) by extension 'ros'
[0.391s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/3.22.1/CompilerIdCXX) by extensions ['cmake', 'python']
[0.391s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/3.22.1/CompilerIdCXX) by extension 'cmake'
[0.391s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/3.22.1/CompilerIdCXX) by extension 'python'
[0.391s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/3.22.1/CompilerIdCXX) by extensions ['python_setup_py']
[0.391s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/3.22.1/CompilerIdCXX) by extension 'python_setup_py'
[0.392s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/3.22.1/CompilerIdCXX/tmp) by extensions ['ignore', 'ignore_ament_install']
[0.392s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/3.22.1/CompilerIdCXX/tmp) by extension 'ignore'
[0.392s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/3.22.1/CompilerIdCXX/tmp) by extension 'ignore_ament_install'
[0.392s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/3.22.1/CompilerIdCXX/tmp) by extensions ['colcon_pkg']
[0.392s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/3.22.1/CompilerIdCXX/tmp) by extension 'colcon_pkg'
[0.392s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/3.22.1/CompilerIdCXX/tmp) by extensions ['colcon_meta']
[0.392s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/3.22.1/CompilerIdCXX/tmp) by extension 'colcon_meta'
[0.392s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/3.22.1/CompilerIdCXX/tmp) by extensions ['ros']
[0.392s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/3.22.1/CompilerIdCXX/tmp) by extension 'ros'
[0.392s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/3.22.1/CompilerIdCXX/tmp) by extensions ['cmake', 'python']
[0.392s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/3.22.1/CompilerIdCXX/tmp) by extension 'cmake'
[0.392s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/3.22.1/CompilerIdCXX/tmp) by extension 'python'
[0.392s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/3.22.1/CompilerIdCXX/tmp) by extensions ['python_setup_py']
[0.392s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/3.22.1/CompilerIdCXX/tmp) by extension 'python_setup_py'
[0.392s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/CMakeTmp) by extensions ['ignore', 'ignore_ament_install']
[0.392s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/CMakeTmp) by extension 'ignore'
[0.393s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/CMakeTmp) by extension 'ignore_ament_install'
[0.393s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/CMakeTmp) by extensions ['colcon_pkg']
[0.393s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/CMakeTmp) by extension 'colcon_pkg'
[0.393s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/CMakeTmp) by extensions ['colcon_meta']
[0.393s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/CMakeTmp) by extension 'colcon_meta'
[0.393s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/CMakeTmp) by extensions ['ros']
[0.393s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/CMakeTmp) by extension 'ros'
[0.393s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/CMakeTmp) by extensions ['cmake', 'python']
[0.393s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/CMakeTmp) by extension 'cmake'
[0.393s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/CMakeTmp) by extension 'python'
[0.393s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/CMakeTmp) by extensions ['python_setup_py']
[0.393s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/CMakeTmp) by extension 'python_setup_py'
[0.393s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/Export) by extensions ['ignore', 'ignore_ament_install']
[0.393s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/Export) by extension 'ignore'
[0.393s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/Export) by extension 'ignore_ament_install'
[0.393s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/Export) by extensions ['colcon_pkg']
[0.393s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/Export) by extension 'colcon_pkg'
[0.394s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/Export) by extensions ['colcon_meta']
[0.394s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/Export) by extension 'colcon_meta'
[0.394s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/Export) by extensions ['ros']
[0.394s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/Export) by extension 'ros'
[0.394s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/Export) by extensions ['cmake', 'python']
[0.394s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/Export) by extension 'cmake'
[0.394s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/Export) by extension 'python'
[0.394s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/Export) by extensions ['python_setup_py']
[0.394s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/Export) by extension 'python_setup_py'
[0.394s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/Export/share) by extensions ['ignore', 'ignore_ament_install']
[0.394s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/Export/share) by extension 'ignore'
[0.394s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/Export/share) by extension 'ignore_ament_install'
[0.394s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/Export/share) by extensions ['colcon_pkg']
[0.394s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/Export/share) by extension 'colcon_pkg'
[0.394s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/Export/share) by extensions ['colcon_meta']
[0.394s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/Export/share) by extension 'colcon_meta'
[0.394s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/Export/share) by extensions ['ros']
[0.394s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/Export/share) by extension 'ros'
[0.395s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/Export/share) by extensions ['cmake', 'python']
[0.395s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/Export/share) by extension 'cmake'
[0.395s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/Export/share) by extension 'python'
[0.395s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/Export/share) by extensions ['python_setup_py']
[0.395s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/Export/share) by extension 'python_setup_py'
[0.395s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/Export/share/slcan) by extensions ['ignore', 'ignore_ament_install']
[0.395s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/Export/share/slcan) by extension 'ignore'
[0.395s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/Export/share/slcan) by extension 'ignore_ament_install'
[0.395s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/Export/share/slcan) by extensions ['colcon_pkg']
[0.395s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/Export/share/slcan) by extension 'colcon_pkg'
[0.395s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/Export/share/slcan) by extensions ['colcon_meta']
[0.395s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/Export/share/slcan) by extension 'colcon_meta'
[0.395s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/Export/share/slcan) by extensions ['ros']
[0.395s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/Export/share/slcan) by extension 'ros'
[0.395s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/Export/share/slcan) by extensions ['cmake', 'python']
[0.395s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/Export/share/slcan) by extension 'cmake'
[0.395s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/Export/share/slcan) by extension 'python'
[0.396s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/Export/share/slcan) by extensions ['python_setup_py']
[0.396s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/Export/share/slcan) by extension 'python_setup_py'
[0.396s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/Export/share/slcan/cmake) by extensions ['ignore', 'ignore_ament_install']
[0.396s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/Export/share/slcan/cmake) by extension 'ignore'
[0.396s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/Export/share/slcan/cmake) by extension 'ignore_ament_install'
[0.396s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/Export/share/slcan/cmake) by extensions ['colcon_pkg']
[0.396s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/Export/share/slcan/cmake) by extension 'colcon_pkg'
[0.396s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/Export/share/slcan/cmake) by extensions ['colcon_meta']
[0.396s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/Export/share/slcan/cmake) by extension 'colcon_meta'
[0.396s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/Export/share/slcan/cmake) by extensions ['ros']
[0.396s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/Export/share/slcan/cmake) by extension 'ros'
[0.396s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/Export/share/slcan/cmake) by extensions ['cmake', 'python']
[0.396s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/Export/share/slcan/cmake) by extension 'cmake'
[0.396s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/Export/share/slcan/cmake) by extension 'python'
[0.396s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/Export/share/slcan/cmake) by extensions ['python_setup_py']
[0.396s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/Export/share/slcan/cmake) by extension 'python_setup_py'
[0.397s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/slcan.dir) by extensions ['ignore', 'ignore_ament_install']
[0.397s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/slcan.dir) by extension 'ignore'
[0.397s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/slcan.dir) by extension 'ignore_ament_install'
[0.397s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/slcan.dir) by extensions ['colcon_pkg']
[0.397s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/slcan.dir) by extension 'colcon_pkg'
[0.397s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/slcan.dir) by extensions ['colcon_meta']
[0.397s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/slcan.dir) by extension 'colcon_meta'
[0.397s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/slcan.dir) by extensions ['ros']
[0.397s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/slcan.dir) by extension 'ros'
[0.397s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/slcan.dir) by extensions ['cmake', 'python']
[0.397s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/slcan.dir) by extension 'cmake'
[0.397s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/slcan.dir) by extension 'python'
[0.397s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/slcan.dir) by extensions ['python_setup_py']
[0.397s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/slcan.dir) by extension 'python_setup_py'
[0.397s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/slcan.dir/src) by extensions ['ignore', 'ignore_ament_install']
[0.397s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/slcan.dir/src) by extension 'ignore'
[0.397s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/slcan.dir/src) by extension 'ignore_ament_install'
[0.398s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/slcan.dir/src) by extensions ['colcon_pkg']
[0.398s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/slcan.dir/src) by extension 'colcon_pkg'
[0.398s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/slcan.dir/src) by extensions ['colcon_meta']
[0.398s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/slcan.dir/src) by extension 'colcon_meta'
[0.398s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/slcan.dir/src) by extensions ['ros']
[0.398s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/slcan.dir/src) by extension 'ros'
[0.398s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/slcan.dir/src) by extensions ['cmake', 'python']
[0.398s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/slcan.dir/src) by extension 'cmake'
[0.398s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/slcan.dir/src) by extension 'python'
[0.398s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/slcan.dir/src) by extensions ['python_setup_py']
[0.398s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/CMakeFiles/slcan.dir/src) by extension 'python_setup_py'
[0.398s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/cmake) by extensions ['ignore', 'ignore_ament_install']
[0.398s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/cmake) by extension 'ignore'
[0.398s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/cmake) by extension 'ignore_ament_install'
[0.398s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/cmake) by extensions ['colcon_pkg']
[0.398s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/cmake) by extension 'colcon_pkg'
[0.398s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/cmake) by extensions ['colcon_meta']
[0.398s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/cmake) by extension 'colcon_meta'
[0.399s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/cmake) by extensions ['ros']
[0.399s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/cmake) by extension 'ros'
[0.399s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/cmake) by extensions ['cmake', 'python']
[0.399s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/cmake) by extension 'cmake'
[0.399s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/cmake) by extension 'python'
[0.399s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/cmake) by extensions ['python_setup_py']
[0.399s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/cmake) by extension 'python_setup_py'
[0.399s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/cmake/slcan) by extensions ['ignore', 'ignore_ament_install']
[0.399s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/cmake/slcan) by extension 'ignore'
[0.399s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/cmake/slcan) by extension 'ignore_ament_install'
[0.399s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/cmake/slcan) by extensions ['colcon_pkg']
[0.399s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/cmake/slcan) by extension 'colcon_pkg'
[0.399s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/cmake/slcan) by extensions ['colcon_meta']
[0.399s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/cmake/slcan) by extension 'colcon_meta'
[0.399s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/cmake/slcan) by extensions ['ros']
[0.399s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/cmake/slcan) by extension 'ros'
[0.399s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/cmake/slcan) by extensions ['cmake', 'python']
[0.399s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/cmake/slcan) by extension 'cmake'
[0.400s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/cmake/slcan) by extension 'python'
[0.400s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/cmake/slcan) by extensions ['python_setup_py']
[0.400s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slcan/cmake/slcan) by extension 'python_setup_py'
[0.400s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack) by extensions ['ignore', 'ignore_ament_install']
[0.400s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack) by extension 'ignore'
[0.400s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack) by extension 'ignore_ament_install'
[0.400s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack) by extensions ['colcon_pkg']
[0.400s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack) by extension 'colcon_pkg'
[0.400s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack) by extensions ['colcon_meta']
[0.400s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack) by extension 'colcon_meta'
[0.400s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack) by extensions ['ros']
[0.400s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack) by extension 'ros'
[0.400s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack) by extensions ['cmake', 'python']
[0.400s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack) by extension 'cmake'
[0.400s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack) by extension 'python'
[0.400s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack) by extensions ['python_setup_py']
[0.400s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack) by extension 'python_setup_py'
[0.401s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles) by extensions ['ignore', 'ignore_ament_install']
[0.401s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles) by extension 'ignore'
[0.401s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles) by extension 'ignore_ament_install'
[0.401s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles) by extensions ['colcon_pkg']
[0.401s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles) by extension 'colcon_pkg'
[0.401s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles) by extensions ['colcon_meta']
[0.401s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles) by extension 'colcon_meta'
[0.401s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles) by extensions ['ros']
[0.401s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles) by extension 'ros'
[0.401s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles) by extensions ['cmake', 'python']
[0.401s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles) by extension 'cmake'
[0.401s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles) by extension 'python'
[0.401s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles) by extensions ['python_setup_py']
[0.401s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles) by extension 'python_setup_py'
[0.401s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/3.22.1) by extensions ['ignore', 'ignore_ament_install']
[0.401s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/3.22.1) by extension 'ignore'
[0.401s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/3.22.1) by extension 'ignore_ament_install'
[0.402s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/3.22.1) by extensions ['colcon_pkg']
[0.402s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/3.22.1) by extension 'colcon_pkg'
[0.402s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/3.22.1) by extensions ['colcon_meta']
[0.402s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/3.22.1) by extension 'colcon_meta'
[0.402s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/3.22.1) by extensions ['ros']
[0.402s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/3.22.1) by extension 'ros'
[0.402s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/3.22.1) by extensions ['cmake', 'python']
[0.402s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/3.22.1) by extension 'cmake'
[0.402s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/3.22.1) by extension 'python'
[0.402s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/3.22.1) by extensions ['python_setup_py']
[0.402s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/3.22.1) by extension 'python_setup_py'
[0.402s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/3.22.1/CompilerIdC) by extensions ['ignore', 'ignore_ament_install']
[0.402s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/3.22.1/CompilerIdC) by extension 'ignore'
[0.402s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/3.22.1/CompilerIdC) by extension 'ignore_ament_install'
[0.402s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/3.22.1/CompilerIdC) by extensions ['colcon_pkg']
[0.402s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/3.22.1/CompilerIdC) by extension 'colcon_pkg'
[0.402s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/3.22.1/CompilerIdC) by extensions ['colcon_meta']
[0.402s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/3.22.1/CompilerIdC) by extension 'colcon_meta'
[0.402s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/3.22.1/CompilerIdC) by extensions ['ros']
[0.403s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/3.22.1/CompilerIdC) by extension 'ros'
[0.403s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/3.22.1/CompilerIdC) by extensions ['cmake', 'python']
[0.403s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/3.22.1/CompilerIdC) by extension 'cmake'
[0.403s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/3.22.1/CompilerIdC) by extension 'python'
[0.403s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/3.22.1/CompilerIdC) by extensions ['python_setup_py']
[0.403s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/3.22.1/CompilerIdC) by extension 'python_setup_py'
[0.403s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/3.22.1/CompilerIdC/tmp) by extensions ['ignore', 'ignore_ament_install']
[0.403s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/3.22.1/CompilerIdC/tmp) by extension 'ignore'
[0.403s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/3.22.1/CompilerIdC/tmp) by extension 'ignore_ament_install'
[0.403s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/3.22.1/CompilerIdC/tmp) by extensions ['colcon_pkg']
[0.403s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/3.22.1/CompilerIdC/tmp) by extension 'colcon_pkg'
[0.403s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/3.22.1/CompilerIdC/tmp) by extensions ['colcon_meta']
[0.403s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/3.22.1/CompilerIdC/tmp) by extension 'colcon_meta'
[0.403s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/3.22.1/CompilerIdC/tmp) by extensions ['ros']
[0.403s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/3.22.1/CompilerIdC/tmp) by extension 'ros'
[0.403s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/3.22.1/CompilerIdC/tmp) by extensions ['cmake', 'python']
[0.403s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/3.22.1/CompilerIdC/tmp) by extension 'cmake'
[0.404s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/3.22.1/CompilerIdC/tmp) by extension 'python'
[0.404s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/3.22.1/CompilerIdC/tmp) by extensions ['python_setup_py']
[0.404s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/3.22.1/CompilerIdC/tmp) by extension 'python_setup_py'
[0.404s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/3.22.1/CompilerIdCXX) by extensions ['ignore', 'ignore_ament_install']
[0.404s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/3.22.1/CompilerIdCXX) by extension 'ignore'
[0.404s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/3.22.1/CompilerIdCXX) by extension 'ignore_ament_install'
[0.404s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/3.22.1/CompilerIdCXX) by extensions ['colcon_pkg']
[0.404s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/3.22.1/CompilerIdCXX) by extension 'colcon_pkg'
[0.404s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/3.22.1/CompilerIdCXX) by extensions ['colcon_meta']
[0.404s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/3.22.1/CompilerIdCXX) by extension 'colcon_meta'
[0.404s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/3.22.1/CompilerIdCXX) by extensions ['ros']
[0.404s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/3.22.1/CompilerIdCXX) by extension 'ros'
[0.404s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/3.22.1/CompilerIdCXX) by extensions ['cmake', 'python']
[0.404s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/3.22.1/CompilerIdCXX) by extension 'cmake'
[0.404s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/3.22.1/CompilerIdCXX) by extension 'python'
[0.404s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/3.22.1/CompilerIdCXX) by extensions ['python_setup_py']
[0.404s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/3.22.1/CompilerIdCXX) by extension 'python_setup_py'
[0.405s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/3.22.1/CompilerIdCXX/tmp) by extensions ['ignore', 'ignore_ament_install']
[0.405s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/3.22.1/CompilerIdCXX/tmp) by extension 'ignore'
[0.405s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/3.22.1/CompilerIdCXX/tmp) by extension 'ignore_ament_install'
[0.405s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/3.22.1/CompilerIdCXX/tmp) by extensions ['colcon_pkg']
[0.405s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/3.22.1/CompilerIdCXX/tmp) by extension 'colcon_pkg'
[0.405s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/3.22.1/CompilerIdCXX/tmp) by extensions ['colcon_meta']
[0.405s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/3.22.1/CompilerIdCXX/tmp) by extension 'colcon_meta'
[0.405s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/3.22.1/CompilerIdCXX/tmp) by extensions ['ros']
[0.405s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/3.22.1/CompilerIdCXX/tmp) by extension 'ros'
[0.405s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/3.22.1/CompilerIdCXX/tmp) by extensions ['cmake', 'python']
[0.405s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/3.22.1/CompilerIdCXX/tmp) by extension 'cmake'
[0.405s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/3.22.1/CompilerIdCXX/tmp) by extension 'python'
[0.405s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/3.22.1/CompilerIdCXX/tmp) by extensions ['python_setup_py']
[0.405s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/3.22.1/CompilerIdCXX/tmp) by extension 'python_setup_py'
[0.405s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/CMakeTmp) by extensions ['ignore', 'ignore_ament_install']
[0.405s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/CMakeTmp) by extension 'ignore'
[0.406s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/CMakeTmp) by extension 'ignore_ament_install'
[0.406s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/CMakeTmp) by extensions ['colcon_pkg']
[0.406s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/CMakeTmp) by extension 'colcon_pkg'
[0.406s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/CMakeTmp) by extensions ['colcon_meta']
[0.406s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/CMakeTmp) by extension 'colcon_meta'
[0.406s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/CMakeTmp) by extensions ['ros']
[0.406s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/CMakeTmp) by extension 'ros'
[0.406s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/CMakeTmp) by extensions ['cmake', 'python']
[0.406s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/CMakeTmp) by extension 'cmake'
[0.406s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/CMakeTmp) by extension 'python'
[0.406s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/CMakeTmp) by extensions ['python_setup_py']
[0.406s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/CMakeTmp) by extension 'python_setup_py'
[0.406s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/Export) by extensions ['ignore', 'ignore_ament_install']
[0.406s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/Export) by extension 'ignore'
[0.406s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/Export) by extension 'ignore_ament_install'
[0.406s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/Export) by extensions ['colcon_pkg']
[0.406s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/Export) by extension 'colcon_pkg'
[0.406s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/Export) by extensions ['colcon_meta']
[0.407s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/Export) by extension 'colcon_meta'
[0.407s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/Export) by extensions ['ros']
[0.407s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/Export) by extension 'ros'
[0.407s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/Export) by extensions ['cmake', 'python']
[0.407s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/Export) by extension 'cmake'
[0.407s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/Export) by extension 'python'
[0.407s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/Export) by extensions ['python_setup_py']
[0.407s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/Export) by extension 'python_setup_py'
[0.407s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/Export/share) by extensions ['ignore', 'ignore_ament_install']
[0.407s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/Export/share) by extension 'ignore'
[0.407s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/Export/share) by extension 'ignore_ament_install'
[0.407s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/Export/share) by extensions ['colcon_pkg']
[0.407s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/Export/share) by extension 'colcon_pkg'
[0.407s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/Export/share) by extensions ['colcon_meta']
[0.407s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/Export/share) by extension 'colcon_meta'
[0.407s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/Export/share) by extensions ['ros']
[0.407s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/Export/share) by extension 'ros'
[0.408s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/Export/share) by extensions ['cmake', 'python']
[0.408s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/Export/share) by extension 'cmake'
[0.408s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/Export/share) by extension 'python'
[0.408s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/Export/share) by extensions ['python_setup_py']
[0.408s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/Export/share) by extension 'python_setup_py'
[0.408s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/Export/share/slpmu_jack) by extensions ['ignore', 'ignore_ament_install']
[0.408s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/Export/share/slpmu_jack) by extension 'ignore'
[0.408s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/Export/share/slpmu_jack) by extension 'ignore_ament_install'
[0.408s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/Export/share/slpmu_jack) by extensions ['colcon_pkg']
[0.408s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/Export/share/slpmu_jack) by extension 'colcon_pkg'
[0.408s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/Export/share/slpmu_jack) by extensions ['colcon_meta']
[0.408s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/Export/share/slpmu_jack) by extension 'colcon_meta'
[0.408s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/Export/share/slpmu_jack) by extensions ['ros']
[0.408s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/Export/share/slpmu_jack) by extension 'ros'
[0.408s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/Export/share/slpmu_jack) by extensions ['cmake', 'python']
[0.408s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/Export/share/slpmu_jack) by extension 'cmake'
[0.408s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/Export/share/slpmu_jack) by extension 'python'
[0.408s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/Export/share/slpmu_jack) by extensions ['python_setup_py']
[0.408s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/Export/share/slpmu_jack) by extension 'python_setup_py'
[0.409s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/Export/share/slpmu_jack/cmake) by extensions ['ignore', 'ignore_ament_install']
[0.409s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/Export/share/slpmu_jack/cmake) by extension 'ignore'
[0.409s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/Export/share/slpmu_jack/cmake) by extension 'ignore_ament_install'
[0.409s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/Export/share/slpmu_jack/cmake) by extensions ['colcon_pkg']
[0.409s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/Export/share/slpmu_jack/cmake) by extension 'colcon_pkg'
[0.409s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/Export/share/slpmu_jack/cmake) by extensions ['colcon_meta']
[0.409s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/Export/share/slpmu_jack/cmake) by extension 'colcon_meta'
[0.409s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/Export/share/slpmu_jack/cmake) by extensions ['ros']
[0.409s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/Export/share/slpmu_jack/cmake) by extension 'ros'
[0.409s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/Export/share/slpmu_jack/cmake) by extensions ['cmake', 'python']
[0.409s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/Export/share/slpmu_jack/cmake) by extension 'cmake'
[0.409s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/Export/share/slpmu_jack/cmake) by extension 'python'
[0.409s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/Export/share/slpmu_jack/cmake) by extensions ['python_setup_py']
[0.409s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/Export/share/slpmu_jack/cmake) by extension 'python_setup_py'
[0.409s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/jack_utils.dir) by extensions ['ignore', 'ignore_ament_install']
[0.410s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/jack_utils.dir) by extension 'ignore'
[0.410s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/jack_utils.dir) by extension 'ignore_ament_install'
[0.410s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/jack_utils.dir) by extensions ['colcon_pkg']
[0.410s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/jack_utils.dir) by extension 'colcon_pkg'
[0.410s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/jack_utils.dir) by extensions ['colcon_meta']
[0.410s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/jack_utils.dir) by extension 'colcon_meta'
[0.410s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/jack_utils.dir) by extensions ['ros']
[0.410s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/jack_utils.dir) by extension 'ros'
[0.410s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/jack_utils.dir) by extensions ['cmake', 'python']
[0.410s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/jack_utils.dir) by extension 'cmake'
[0.410s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/jack_utils.dir) by extension 'python'
[0.410s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/jack_utils.dir) by extensions ['python_setup_py']
[0.410s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/jack_utils.dir) by extension 'python_setup_py'
[0.410s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/jack_utils.dir/bin) by extensions ['ignore', 'ignore_ament_install']
[0.410s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/jack_utils.dir/bin) by extension 'ignore'
[0.410s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/jack_utils.dir/bin) by extension 'ignore_ament_install'
[0.411s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/jack_utils.dir/bin) by extensions ['colcon_pkg']
[0.411s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/jack_utils.dir/bin) by extension 'colcon_pkg'
[0.411s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/jack_utils.dir/bin) by extensions ['colcon_meta']
[0.411s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/jack_utils.dir/bin) by extension 'colcon_meta'
[0.411s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/jack_utils.dir/bin) by extensions ['ros']
[0.411s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/jack_utils.dir/bin) by extension 'ros'
[0.411s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/jack_utils.dir/bin) by extensions ['cmake', 'python']
[0.411s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/jack_utils.dir/bin) by extension 'cmake'
[0.411s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/jack_utils.dir/bin) by extension 'python'
[0.411s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/jack_utils.dir/bin) by extensions ['python_setup_py']
[0.411s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/jack_utils.dir/bin) by extension 'python_setup_py'
[0.411s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/slpmu_jack.dir) by extensions ['ignore', 'ignore_ament_install']
[0.411s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/slpmu_jack.dir) by extension 'ignore'
[0.411s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/slpmu_jack.dir) by extension 'ignore_ament_install'
[0.411s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/slpmu_jack.dir) by extensions ['colcon_pkg']
[0.411s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/slpmu_jack.dir) by extension 'colcon_pkg'
[0.411s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/slpmu_jack.dir) by extensions ['colcon_meta']
[0.411s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/slpmu_jack.dir) by extension 'colcon_meta'
[0.412s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/slpmu_jack.dir) by extensions ['ros']
[0.412s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/slpmu_jack.dir) by extension 'ros'
[0.412s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/slpmu_jack.dir) by extensions ['cmake', 'python']
[0.412s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/slpmu_jack.dir) by extension 'cmake'
[0.412s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/slpmu_jack.dir) by extension 'python'
[0.412s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/slpmu_jack.dir) by extensions ['python_setup_py']
[0.412s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/slpmu_jack.dir) by extension 'python_setup_py'
[0.412s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/slpmu_jack.dir/src) by extensions ['ignore', 'ignore_ament_install']
[0.412s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/slpmu_jack.dir/src) by extension 'ignore'
[0.412s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/slpmu_jack.dir/src) by extension 'ignore_ament_install'
[0.412s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/slpmu_jack.dir/src) by extensions ['colcon_pkg']
[0.412s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/slpmu_jack.dir/src) by extension 'colcon_pkg'
[0.412s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/slpmu_jack.dir/src) by extensions ['colcon_meta']
[0.412s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/slpmu_jack.dir/src) by extension 'colcon_meta'
[0.412s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/slpmu_jack.dir/src) by extensions ['ros']
[0.412s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/slpmu_jack.dir/src) by extension 'ros'
[0.412s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/slpmu_jack.dir/src) by extensions ['cmake', 'python']
[0.412s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/slpmu_jack.dir/src) by extension 'cmake'
[0.413s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/slpmu_jack.dir/src) by extension 'python'
[0.413s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/slpmu_jack.dir/src) by extensions ['python_setup_py']
[0.413s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/CMakeFiles/slpmu_jack.dir/src) by extension 'python_setup_py'
[0.413s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/cmake) by extensions ['ignore', 'ignore_ament_install']
[0.413s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/cmake) by extension 'ignore'
[0.413s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/cmake) by extension 'ignore_ament_install'
[0.413s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/cmake) by extensions ['colcon_pkg']
[0.413s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/cmake) by extension 'colcon_pkg'
[0.413s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/cmake) by extensions ['colcon_meta']
[0.413s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/cmake) by extension 'colcon_meta'
[0.413s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/cmake) by extensions ['ros']
[0.413s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/cmake) by extension 'ros'
[0.413s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/cmake) by extensions ['cmake', 'python']
[0.413s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/cmake) by extension 'cmake'
[0.413s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/cmake) by extension 'python'
[0.413s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/cmake) by extensions ['python_setup_py']
[0.413s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/cmake) by extension 'python_setup_py'
[0.414s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/cmake/slpmu_jack) by extensions ['ignore', 'ignore_ament_install']
[0.414s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/cmake/slpmu_jack) by extension 'ignore'
[0.414s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/cmake/slpmu_jack) by extension 'ignore_ament_install'
[0.414s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/cmake/slpmu_jack) by extensions ['colcon_pkg']
[0.414s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/cmake/slpmu_jack) by extension 'colcon_pkg'
[0.414s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/cmake/slpmu_jack) by extensions ['colcon_meta']
[0.414s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/cmake/slpmu_jack) by extension 'colcon_meta'
[0.414s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/cmake/slpmu_jack) by extensions ['ros']
[0.414s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/cmake/slpmu_jack) by extension 'ros'
[0.414s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/cmake/slpmu_jack) by extensions ['cmake', 'python']
[0.414s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/cmake/slpmu_jack) by extension 'cmake'
[0.414s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/cmake/slpmu_jack) by extension 'python'
[0.414s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/cmake/slpmu_jack) by extensions ['python_setup_py']
[0.414s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_jack/cmake/slpmu_jack) by extension 'python_setup_py'
[0.414s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor) by extensions ['ignore', 'ignore_ament_install']
[0.414s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor) by extension 'ignore'
[0.414s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor) by extension 'ignore_ament_install'
[0.415s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor) by extensions ['colcon_pkg']
[0.415s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor) by extension 'colcon_pkg'
[0.415s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor) by extensions ['colcon_meta']
[0.415s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor) by extension 'colcon_meta'
[0.415s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor) by extensions ['ros']
[0.415s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor) by extension 'ros'
[0.415s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor) by extensions ['cmake', 'python']
[0.415s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor) by extension 'cmake'
[0.415s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor) by extension 'python'
[0.415s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor) by extensions ['python_setup_py']
[0.415s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor) by extension 'python_setup_py'
[0.415s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles) by extensions ['ignore', 'ignore_ament_install']
[0.415s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles) by extension 'ignore'
[0.415s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles) by extension 'ignore_ament_install'
[0.415s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles) by extensions ['colcon_pkg']
[0.415s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles) by extension 'colcon_pkg'
[0.415s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles) by extensions ['colcon_meta']
[0.415s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles) by extension 'colcon_meta'
[0.415s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles) by extensions ['ros']
[0.416s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles) by extension 'ros'
[0.416s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles) by extensions ['cmake', 'python']
[0.416s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles) by extension 'cmake'
[0.416s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles) by extension 'python'
[0.416s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles) by extensions ['python_setup_py']
[0.416s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles) by extension 'python_setup_py'
[0.416s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/3.22.1) by extensions ['ignore', 'ignore_ament_install']
[0.416s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/3.22.1) by extension 'ignore'
[0.416s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/3.22.1) by extension 'ignore_ament_install'
[0.416s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/3.22.1) by extensions ['colcon_pkg']
[0.416s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/3.22.1) by extension 'colcon_pkg'
[0.416s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/3.22.1) by extensions ['colcon_meta']
[0.416s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/3.22.1) by extension 'colcon_meta'
[0.416s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/3.22.1) by extensions ['ros']
[0.416s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/3.22.1) by extension 'ros'
[0.416s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/3.22.1) by extensions ['cmake', 'python']
[0.416s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/3.22.1) by extension 'cmake'
[0.417s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/3.22.1) by extension 'python'
[0.417s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/3.22.1) by extensions ['python_setup_py']
[0.417s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/3.22.1) by extension 'python_setup_py'
[0.417s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/3.22.1/CompilerIdC) by extensions ['ignore', 'ignore_ament_install']
[0.417s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/3.22.1/CompilerIdC) by extension 'ignore'
[0.417s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/3.22.1/CompilerIdC) by extension 'ignore_ament_install'
[0.417s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/3.22.1/CompilerIdC) by extensions ['colcon_pkg']
[0.417s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/3.22.1/CompilerIdC) by extension 'colcon_pkg'
[0.417s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/3.22.1/CompilerIdC) by extensions ['colcon_meta']
[0.417s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/3.22.1/CompilerIdC) by extension 'colcon_meta'
[0.417s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/3.22.1/CompilerIdC) by extensions ['ros']
[0.417s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/3.22.1/CompilerIdC) by extension 'ros'
[0.417s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/3.22.1/CompilerIdC) by extensions ['cmake', 'python']
[0.417s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/3.22.1/CompilerIdC) by extension 'cmake'
[0.417s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/3.22.1/CompilerIdC) by extension 'python'
[0.417s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/3.22.1/CompilerIdC) by extensions ['python_setup_py']
[0.417s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/3.22.1/CompilerIdC) by extension 'python_setup_py'
[0.418s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/3.22.1/CompilerIdC/tmp) by extensions ['ignore', 'ignore_ament_install']
[0.418s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/3.22.1/CompilerIdC/tmp) by extension 'ignore'
[0.418s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/3.22.1/CompilerIdC/tmp) by extension 'ignore_ament_install'
[0.418s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/3.22.1/CompilerIdC/tmp) by extensions ['colcon_pkg']
[0.418s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/3.22.1/CompilerIdC/tmp) by extension 'colcon_pkg'
[0.418s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/3.22.1/CompilerIdC/tmp) by extensions ['colcon_meta']
[0.418s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/3.22.1/CompilerIdC/tmp) by extension 'colcon_meta'
[0.418s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/3.22.1/CompilerIdC/tmp) by extensions ['ros']
[0.418s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/3.22.1/CompilerIdC/tmp) by extension 'ros'
[0.418s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/3.22.1/CompilerIdC/tmp) by extensions ['cmake', 'python']
[0.418s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/3.22.1/CompilerIdC/tmp) by extension 'cmake'
[0.418s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/3.22.1/CompilerIdC/tmp) by extension 'python'
[0.418s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/3.22.1/CompilerIdC/tmp) by extensions ['python_setup_py']
[0.418s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/3.22.1/CompilerIdC/tmp) by extension 'python_setup_py'
[0.418s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/3.22.1/CompilerIdCXX) by extensions ['ignore', 'ignore_ament_install']
[0.418s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/3.22.1/CompilerIdCXX) by extension 'ignore'
[0.418s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/3.22.1/CompilerIdCXX) by extension 'ignore_ament_install'
[0.419s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/3.22.1/CompilerIdCXX) by extensions ['colcon_pkg']
[0.419s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/3.22.1/CompilerIdCXX) by extension 'colcon_pkg'
[0.419s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/3.22.1/CompilerIdCXX) by extensions ['colcon_meta']
[0.419s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/3.22.1/CompilerIdCXX) by extension 'colcon_meta'
[0.419s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/3.22.1/CompilerIdCXX) by extensions ['ros']
[0.419s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/3.22.1/CompilerIdCXX) by extension 'ros'
[0.419s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/3.22.1/CompilerIdCXX) by extensions ['cmake', 'python']
[0.419s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/3.22.1/CompilerIdCXX) by extension 'cmake'
[0.419s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/3.22.1/CompilerIdCXX) by extension 'python'
[0.419s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/3.22.1/CompilerIdCXX) by extensions ['python_setup_py']
[0.419s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/3.22.1/CompilerIdCXX) by extension 'python_setup_py'
[0.419s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/3.22.1/CompilerIdCXX/tmp) by extensions ['ignore', 'ignore_ament_install']
[0.419s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/3.22.1/CompilerIdCXX/tmp) by extension 'ignore'
[0.419s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/3.22.1/CompilerIdCXX/tmp) by extension 'ignore_ament_install'
[0.419s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/3.22.1/CompilerIdCXX/tmp) by extensions ['colcon_pkg']
[0.419s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/3.22.1/CompilerIdCXX/tmp) by extension 'colcon_pkg'
[0.419s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/3.22.1/CompilerIdCXX/tmp) by extensions ['colcon_meta']
[0.419s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/3.22.1/CompilerIdCXX/tmp) by extension 'colcon_meta'
[0.420s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/3.22.1/CompilerIdCXX/tmp) by extensions ['ros']
[0.420s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/3.22.1/CompilerIdCXX/tmp) by extension 'ros'
[0.420s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/3.22.1/CompilerIdCXX/tmp) by extensions ['cmake', 'python']
[0.420s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/3.22.1/CompilerIdCXX/tmp) by extension 'cmake'
[0.420s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/3.22.1/CompilerIdCXX/tmp) by extension 'python'
[0.420s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/3.22.1/CompilerIdCXX/tmp) by extensions ['python_setup_py']
[0.420s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/3.22.1/CompilerIdCXX/tmp) by extension 'python_setup_py'
[0.420s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/CMakeTmp) by extensions ['ignore', 'ignore_ament_install']
[0.420s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/CMakeTmp) by extension 'ignore'
[0.420s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/CMakeTmp) by extension 'ignore_ament_install'
[0.420s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/CMakeTmp) by extensions ['colcon_pkg']
[0.420s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/CMakeTmp) by extension 'colcon_pkg'
[0.420s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/CMakeTmp) by extensions ['colcon_meta']
[0.420s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/CMakeTmp) by extension 'colcon_meta'
[0.420s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/CMakeTmp) by extensions ['ros']
[0.420s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/CMakeTmp) by extension 'ros'
[0.420s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/CMakeTmp) by extensions ['cmake', 'python']
[0.420s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/CMakeTmp) by extension 'cmake'
[0.421s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/CMakeTmp) by extension 'python'
[0.421s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/CMakeTmp) by extensions ['python_setup_py']
[0.421s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/CMakeTmp) by extension 'python_setup_py'
[0.421s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/Export) by extensions ['ignore', 'ignore_ament_install']
[0.421s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/Export) by extension 'ignore'
[0.421s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/Export) by extension 'ignore_ament_install'
[0.421s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/Export) by extensions ['colcon_pkg']
[0.421s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/Export) by extension 'colcon_pkg'
[0.421s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/Export) by extensions ['colcon_meta']
[0.421s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/Export) by extension 'colcon_meta'
[0.421s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/Export) by extensions ['ros']
[0.421s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/Export) by extension 'ros'
[0.421s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/Export) by extensions ['cmake', 'python']
[0.421s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/Export) by extension 'cmake'
[0.421s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/Export) by extension 'python'
[0.421s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/Export) by extensions ['python_setup_py']
[0.421s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/Export) by extension 'python_setup_py'
[0.422s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/Export/share) by extensions ['ignore', 'ignore_ament_install']
[0.422s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/Export/share) by extension 'ignore'
[0.422s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/Export/share) by extension 'ignore_ament_install'
[0.422s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/Export/share) by extensions ['colcon_pkg']
[0.422s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/Export/share) by extension 'colcon_pkg'
[0.422s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/Export/share) by extensions ['colcon_meta']
[0.422s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/Export/share) by extension 'colcon_meta'
[0.422s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/Export/share) by extensions ['ros']
[0.422s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/Export/share) by extension 'ros'
[0.422s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/Export/share) by extensions ['cmake', 'python']
[0.422s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/Export/share) by extension 'cmake'
[0.422s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/Export/share) by extension 'python'
[0.422s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/Export/share) by extensions ['python_setup_py']
[0.422s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/Export/share) by extension 'python_setup_py'
[0.422s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/Export/share/slpmu_motor) by extensions ['ignore', 'ignore_ament_install']
[0.422s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/Export/share/slpmu_motor) by extension 'ignore'
[0.422s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/Export/share/slpmu_motor) by extension 'ignore_ament_install'
[0.423s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/Export/share/slpmu_motor) by extensions ['colcon_pkg']
[0.423s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/Export/share/slpmu_motor) by extension 'colcon_pkg'
[0.423s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/Export/share/slpmu_motor) by extensions ['colcon_meta']
[0.423s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/Export/share/slpmu_motor) by extension 'colcon_meta'
[0.423s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/Export/share/slpmu_motor) by extensions ['ros']
[0.423s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/Export/share/slpmu_motor) by extension 'ros'
[0.423s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/Export/share/slpmu_motor) by extensions ['cmake', 'python']
[0.423s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/Export/share/slpmu_motor) by extension 'cmake'
[0.423s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/Export/share/slpmu_motor) by extension 'python'
[0.423s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/Export/share/slpmu_motor) by extensions ['python_setup_py']
[0.423s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/Export/share/slpmu_motor) by extension 'python_setup_py'
[0.423s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/Export/share/slpmu_motor/cmake) by extensions ['ignore', 'ignore_ament_install']
[0.423s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/Export/share/slpmu_motor/cmake) by extension 'ignore'
[0.423s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/Export/share/slpmu_motor/cmake) by extension 'ignore_ament_install'
[0.423s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/Export/share/slpmu_motor/cmake) by extensions ['colcon_pkg']
[0.423s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/Export/share/slpmu_motor/cmake) by extension 'colcon_pkg'
[0.424s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/Export/share/slpmu_motor/cmake) by extensions ['colcon_meta']
[0.424s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/Export/share/slpmu_motor/cmake) by extension 'colcon_meta'
[0.424s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/Export/share/slpmu_motor/cmake) by extensions ['ros']
[0.424s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/Export/share/slpmu_motor/cmake) by extension 'ros'
[0.424s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/Export/share/slpmu_motor/cmake) by extensions ['cmake', 'python']
[0.424s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/Export/share/slpmu_motor/cmake) by extension 'cmake'
[0.424s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/Export/share/slpmu_motor/cmake) by extension 'python'
[0.424s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/Export/share/slpmu_motor/cmake) by extensions ['python_setup_py']
[0.424s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/Export/share/slpmu_motor/cmake) by extension 'python_setup_py'
[0.424s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/motor_utils.dir) by extensions ['ignore', 'ignore_ament_install']
[0.424s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/motor_utils.dir) by extension 'ignore'
[0.424s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/motor_utils.dir) by extension 'ignore_ament_install'
[0.424s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/motor_utils.dir) by extensions ['colcon_pkg']
[0.424s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/motor_utils.dir) by extension 'colcon_pkg'
[0.424s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/motor_utils.dir) by extensions ['colcon_meta']
[0.424s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/motor_utils.dir) by extension 'colcon_meta'
[0.424s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/motor_utils.dir) by extensions ['ros']
[0.425s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/motor_utils.dir) by extension 'ros'
[0.425s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/motor_utils.dir) by extensions ['cmake', 'python']
[0.425s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/motor_utils.dir) by extension 'cmake'
[0.425s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/motor_utils.dir) by extension 'python'
[0.425s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/motor_utils.dir) by extensions ['python_setup_py']
[0.425s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/motor_utils.dir) by extension 'python_setup_py'
[0.425s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/motor_utils.dir/bin) by extensions ['ignore', 'ignore_ament_install']
[0.425s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/motor_utils.dir/bin) by extension 'ignore'
[0.425s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/motor_utils.dir/bin) by extension 'ignore_ament_install'
[0.425s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/motor_utils.dir/bin) by extensions ['colcon_pkg']
[0.425s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/motor_utils.dir/bin) by extension 'colcon_pkg'
[0.425s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/motor_utils.dir/bin) by extensions ['colcon_meta']
[0.425s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/motor_utils.dir/bin) by extension 'colcon_meta'
[0.425s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/motor_utils.dir/bin) by extensions ['ros']
[0.425s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/motor_utils.dir/bin) by extension 'ros'
[0.425s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/motor_utils.dir/bin) by extensions ['cmake', 'python']
[0.425s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/motor_utils.dir/bin) by extension 'cmake'
[0.425s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/motor_utils.dir/bin) by extension 'python'
[0.426s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/motor_utils.dir/bin) by extensions ['python_setup_py']
[0.426s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/motor_utils.dir/bin) by extension 'python_setup_py'
[0.426s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/slpmu_motor.dir) by extensions ['ignore', 'ignore_ament_install']
[0.426s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/slpmu_motor.dir) by extension 'ignore'
[0.426s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/slpmu_motor.dir) by extension 'ignore_ament_install'
[0.426s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/slpmu_motor.dir) by extensions ['colcon_pkg']
[0.426s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/slpmu_motor.dir) by extension 'colcon_pkg'
[0.426s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/slpmu_motor.dir) by extensions ['colcon_meta']
[0.426s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/slpmu_motor.dir) by extension 'colcon_meta'
[0.426s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/slpmu_motor.dir) by extensions ['ros']
[0.426s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/slpmu_motor.dir) by extension 'ros'
[0.426s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/slpmu_motor.dir) by extensions ['cmake', 'python']
[0.426s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/slpmu_motor.dir) by extension 'cmake'
[0.426s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/slpmu_motor.dir) by extension 'python'
[0.426s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/slpmu_motor.dir) by extensions ['python_setup_py']
[0.426s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/slpmu_motor.dir) by extension 'python_setup_py'
[0.426s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/slpmu_motor.dir/src) by extensions ['ignore', 'ignore_ament_install']
[0.427s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/slpmu_motor.dir/src) by extension 'ignore'
[0.427s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/slpmu_motor.dir/src) by extension 'ignore_ament_install'
[0.427s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/slpmu_motor.dir/src) by extensions ['colcon_pkg']
[0.427s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/slpmu_motor.dir/src) by extension 'colcon_pkg'
[0.427s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/slpmu_motor.dir/src) by extensions ['colcon_meta']
[0.427s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/slpmu_motor.dir/src) by extension 'colcon_meta'
[0.427s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/slpmu_motor.dir/src) by extensions ['ros']
[0.427s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/slpmu_motor.dir/src) by extension 'ros'
[0.427s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/slpmu_motor.dir/src) by extensions ['cmake', 'python']
[0.427s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/slpmu_motor.dir/src) by extension 'cmake'
[0.427s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/slpmu_motor.dir/src) by extension 'python'
[0.427s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/slpmu_motor.dir/src) by extensions ['python_setup_py']
[0.427s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/CMakeFiles/slpmu_motor.dir/src) by extension 'python_setup_py'
[0.427s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/cmake) by extensions ['ignore', 'ignore_ament_install']
[0.427s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/cmake) by extension 'ignore'
[0.427s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/cmake) by extension 'ignore_ament_install'
[0.428s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/cmake) by extensions ['colcon_pkg']
[0.428s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/cmake) by extension 'colcon_pkg'
[0.428s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/cmake) by extensions ['colcon_meta']
[0.428s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/cmake) by extension 'colcon_meta'
[0.428s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/cmake) by extensions ['ros']
[0.428s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/cmake) by extension 'ros'
[0.428s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/cmake) by extensions ['cmake', 'python']
[0.428s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/cmake) by extension 'cmake'
[0.428s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/cmake) by extension 'python'
[0.428s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/cmake) by extensions ['python_setup_py']
[0.428s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/cmake) by extension 'python_setup_py'
[0.428s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/cmake/slpmu_motor) by extensions ['ignore', 'ignore_ament_install']
[0.428s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/cmake/slpmu_motor) by extension 'ignore'
[0.428s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/cmake/slpmu_motor) by extension 'ignore_ament_install'
[0.428s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/cmake/slpmu_motor) by extensions ['colcon_pkg']
[0.428s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/cmake/slpmu_motor) by extension 'colcon_pkg'
[0.428s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/cmake/slpmu_motor) by extensions ['colcon_meta']
[0.428s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/cmake/slpmu_motor) by extension 'colcon_meta'
[0.428s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/cmake/slpmu_motor) by extensions ['ros']
[0.428s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/cmake/slpmu_motor) by extension 'ros'
[0.429s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/cmake/slpmu_motor) by extensions ['cmake', 'python']
[0.429s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/cmake/slpmu_motor) by extension 'cmake'
[0.429s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/cmake/slpmu_motor) by extension 'python'
[0.429s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/cmake/slpmu_motor) by extensions ['python_setup_py']
[0.429s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_motor/cmake/slpmu_motor) by extension 'python_setup_py'
[0.429s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power) by extensions ['ignore', 'ignore_ament_install']
[0.429s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power) by extension 'ignore'
[0.429s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power) by extension 'ignore_ament_install'
[0.429s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power) by extensions ['colcon_pkg']
[0.429s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power) by extension 'colcon_pkg'
[0.429s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power) by extensions ['colcon_meta']
[0.429s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power) by extension 'colcon_meta'
[0.429s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power) by extensions ['ros']
[0.429s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power) by extension 'ros'
[0.429s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power) by extensions ['cmake', 'python']
[0.429s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power) by extension 'cmake'
[0.429s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power) by extension 'python'
[0.430s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power) by extensions ['python_setup_py']
[0.430s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power) by extension 'python_setup_py'
[0.430s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles) by extensions ['ignore', 'ignore_ament_install']
[0.430s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles) by extension 'ignore'
[0.430s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles) by extension 'ignore_ament_install'
[0.430s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles) by extensions ['colcon_pkg']
[0.430s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles) by extension 'colcon_pkg'
[0.430s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles) by extensions ['colcon_meta']
[0.430s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles) by extension 'colcon_meta'
[0.430s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles) by extensions ['ros']
[0.430s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles) by extension 'ros'
[0.430s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles) by extensions ['cmake', 'python']
[0.430s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles) by extension 'cmake'
[0.430s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles) by extension 'python'
[0.430s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles) by extensions ['python_setup_py']
[0.430s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles) by extension 'python_setup_py'
[0.430s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/3.22.1) by extensions ['ignore', 'ignore_ament_install']
[0.431s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/3.22.1) by extension 'ignore'
[0.431s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/3.22.1) by extension 'ignore_ament_install'
[0.431s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/3.22.1) by extensions ['colcon_pkg']
[0.431s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/3.22.1) by extension 'colcon_pkg'
[0.431s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/3.22.1) by extensions ['colcon_meta']
[0.431s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/3.22.1) by extension 'colcon_meta'
[0.431s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/3.22.1) by extensions ['ros']
[0.431s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/3.22.1) by extension 'ros'
[0.431s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/3.22.1) by extensions ['cmake', 'python']
[0.431s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/3.22.1) by extension 'cmake'
[0.431s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/3.22.1) by extension 'python'
[0.431s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/3.22.1) by extensions ['python_setup_py']
[0.431s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/3.22.1) by extension 'python_setup_py'
[0.431s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/3.22.1/CompilerIdC) by extensions ['ignore', 'ignore_ament_install']
[0.431s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/3.22.1/CompilerIdC) by extension 'ignore'
[0.431s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/3.22.1/CompilerIdC) by extension 'ignore_ament_install'
[0.431s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/3.22.1/CompilerIdC) by extensions ['colcon_pkg']
[0.431s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/3.22.1/CompilerIdC) by extension 'colcon_pkg'
[0.432s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/3.22.1/CompilerIdC) by extensions ['colcon_meta']
[0.432s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/3.22.1/CompilerIdC) by extension 'colcon_meta'
[0.432s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/3.22.1/CompilerIdC) by extensions ['ros']
[0.432s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/3.22.1/CompilerIdC) by extension 'ros'
[0.432s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/3.22.1/CompilerIdC) by extensions ['cmake', 'python']
[0.432s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/3.22.1/CompilerIdC) by extension 'cmake'
[0.432s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/3.22.1/CompilerIdC) by extension 'python'
[0.432s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/3.22.1/CompilerIdC) by extensions ['python_setup_py']
[0.432s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/3.22.1/CompilerIdC) by extension 'python_setup_py'
[0.432s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/3.22.1/CompilerIdC/tmp) by extensions ['ignore', 'ignore_ament_install']
[0.432s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/3.22.1/CompilerIdC/tmp) by extension 'ignore'
[0.432s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/3.22.1/CompilerIdC/tmp) by extension 'ignore_ament_install'
[0.432s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/3.22.1/CompilerIdC/tmp) by extensions ['colcon_pkg']
[0.432s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/3.22.1/CompilerIdC/tmp) by extension 'colcon_pkg'
[0.432s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/3.22.1/CompilerIdC/tmp) by extensions ['colcon_meta']
[0.432s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/3.22.1/CompilerIdC/tmp) by extension 'colcon_meta'
[0.432s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/3.22.1/CompilerIdC/tmp) by extensions ['ros']
[0.432s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/3.22.1/CompilerIdC/tmp) by extension 'ros'
[0.433s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/3.22.1/CompilerIdC/tmp) by extensions ['cmake', 'python']
[0.433s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/3.22.1/CompilerIdC/tmp) by extension 'cmake'
[0.433s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/3.22.1/CompilerIdC/tmp) by extension 'python'
[0.433s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/3.22.1/CompilerIdC/tmp) by extensions ['python_setup_py']
[0.433s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/3.22.1/CompilerIdC/tmp) by extension 'python_setup_py'
[0.433s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/3.22.1/CompilerIdCXX) by extensions ['ignore', 'ignore_ament_install']
[0.433s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/3.22.1/CompilerIdCXX) by extension 'ignore'
[0.433s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/3.22.1/CompilerIdCXX) by extension 'ignore_ament_install'
[0.433s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/3.22.1/CompilerIdCXX) by extensions ['colcon_pkg']
[0.433s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/3.22.1/CompilerIdCXX) by extension 'colcon_pkg'
[0.433s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/3.22.1/CompilerIdCXX) by extensions ['colcon_meta']
[0.433s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/3.22.1/CompilerIdCXX) by extension 'colcon_meta'
[0.433s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/3.22.1/CompilerIdCXX) by extensions ['ros']
[0.433s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/3.22.1/CompilerIdCXX) by extension 'ros'
[0.433s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/3.22.1/CompilerIdCXX) by extensions ['cmake', 'python']
[0.433s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/3.22.1/CompilerIdCXX) by extension 'cmake'
[0.434s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/3.22.1/CompilerIdCXX) by extension 'python'
[0.434s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/3.22.1/CompilerIdCXX) by extensions ['python_setup_py']
[0.434s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/3.22.1/CompilerIdCXX) by extension 'python_setup_py'
[0.434s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/3.22.1/CompilerIdCXX/tmp) by extensions ['ignore', 'ignore_ament_install']
[0.434s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/3.22.1/CompilerIdCXX/tmp) by extension 'ignore'
[0.434s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/3.22.1/CompilerIdCXX/tmp) by extension 'ignore_ament_install'
[0.434s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/3.22.1/CompilerIdCXX/tmp) by extensions ['colcon_pkg']
[0.434s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/3.22.1/CompilerIdCXX/tmp) by extension 'colcon_pkg'
[0.434s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/3.22.1/CompilerIdCXX/tmp) by extensions ['colcon_meta']
[0.434s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/3.22.1/CompilerIdCXX/tmp) by extension 'colcon_meta'
[0.434s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/3.22.1/CompilerIdCXX/tmp) by extensions ['ros']
[0.434s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/3.22.1/CompilerIdCXX/tmp) by extension 'ros'
[0.434s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/3.22.1/CompilerIdCXX/tmp) by extensions ['cmake', 'python']
[0.434s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/3.22.1/CompilerIdCXX/tmp) by extension 'cmake'
[0.434s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/3.22.1/CompilerIdCXX/tmp) by extension 'python'
[0.434s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/3.22.1/CompilerIdCXX/tmp) by extensions ['python_setup_py']
[0.434s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/3.22.1/CompilerIdCXX/tmp) by extension 'python_setup_py'
[0.435s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/CMakeTmp) by extensions ['ignore', 'ignore_ament_install']
[0.435s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/CMakeTmp) by extension 'ignore'
[0.435s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/CMakeTmp) by extension 'ignore_ament_install'
[0.435s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/CMakeTmp) by extensions ['colcon_pkg']
[0.435s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/CMakeTmp) by extension 'colcon_pkg'
[0.435s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/CMakeTmp) by extensions ['colcon_meta']
[0.435s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/CMakeTmp) by extension 'colcon_meta'
[0.435s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/CMakeTmp) by extensions ['ros']
[0.435s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/CMakeTmp) by extension 'ros'
[0.435s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/CMakeTmp) by extensions ['cmake', 'python']
[0.435s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/CMakeTmp) by extension 'cmake'
[0.435s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/CMakeTmp) by extension 'python'
[0.435s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/CMakeTmp) by extensions ['python_setup_py']
[0.435s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/CMakeTmp) by extension 'python_setup_py'
[0.435s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/Export) by extensions ['ignore', 'ignore_ament_install']
[0.435s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/Export) by extension 'ignore'
[0.435s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/Export) by extension 'ignore_ament_install'
[0.436s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/Export) by extensions ['colcon_pkg']
[0.436s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/Export) by extension 'colcon_pkg'
[0.436s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/Export) by extensions ['colcon_meta']
[0.436s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/Export) by extension 'colcon_meta'
[0.436s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/Export) by extensions ['ros']
[0.436s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/Export) by extension 'ros'
[0.436s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/Export) by extensions ['cmake', 'python']
[0.436s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/Export) by extension 'cmake'
[0.436s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/Export) by extension 'python'
[0.436s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/Export) by extensions ['python_setup_py']
[0.436s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/Export) by extension 'python_setup_py'
[0.436s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/Export/share) by extensions ['ignore', 'ignore_ament_install']
[0.436s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/Export/share) by extension 'ignore'
[0.436s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/Export/share) by extension 'ignore_ament_install'
[0.436s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/Export/share) by extensions ['colcon_pkg']
[0.436s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/Export/share) by extension 'colcon_pkg'
[0.436s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/Export/share) by extensions ['colcon_meta']
[0.436s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/Export/share) by extension 'colcon_meta'
[0.436s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/Export/share) by extensions ['ros']
[0.437s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/Export/share) by extension 'ros'
[0.437s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/Export/share) by extensions ['cmake', 'python']
[0.437s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/Export/share) by extension 'cmake'
[0.437s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/Export/share) by extension 'python'
[0.437s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/Export/share) by extensions ['python_setup_py']
[0.437s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/Export/share) by extension 'python_setup_py'
[0.437s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/Export/share/slpmu_power) by extensions ['ignore', 'ignore_ament_install']
[0.437s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/Export/share/slpmu_power) by extension 'ignore'
[0.437s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/Export/share/slpmu_power) by extension 'ignore_ament_install'
[0.437s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/Export/share/slpmu_power) by extensions ['colcon_pkg']
[0.437s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/Export/share/slpmu_power) by extension 'colcon_pkg'
[0.437s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/Export/share/slpmu_power) by extensions ['colcon_meta']
[0.437s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/Export/share/slpmu_power) by extension 'colcon_meta'
[0.437s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/Export/share/slpmu_power) by extensions ['ros']
[0.437s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/Export/share/slpmu_power) by extension 'ros'
[0.437s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/Export/share/slpmu_power) by extensions ['cmake', 'python']
[0.437s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/Export/share/slpmu_power) by extension 'cmake'
[0.437s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/Export/share/slpmu_power) by extension 'python'
[0.438s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/Export/share/slpmu_power) by extensions ['python_setup_py']
[0.438s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/Export/share/slpmu_power) by extension 'python_setup_py'
[0.438s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/Export/share/slpmu_power/cmake) by extensions ['ignore', 'ignore_ament_install']
[0.438s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/Export/share/slpmu_power/cmake) by extension 'ignore'
[0.438s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/Export/share/slpmu_power/cmake) by extension 'ignore_ament_install'
[0.438s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/Export/share/slpmu_power/cmake) by extensions ['colcon_pkg']
[0.438s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/Export/share/slpmu_power/cmake) by extension 'colcon_pkg'
[0.438s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/Export/share/slpmu_power/cmake) by extensions ['colcon_meta']
[0.438s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/Export/share/slpmu_power/cmake) by extension 'colcon_meta'
[0.438s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/Export/share/slpmu_power/cmake) by extensions ['ros']
[0.438s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/Export/share/slpmu_power/cmake) by extension 'ros'
[0.438s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/Export/share/slpmu_power/cmake) by extensions ['cmake', 'python']
[0.438s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/Export/share/slpmu_power/cmake) by extension 'cmake'
[0.438s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/Export/share/slpmu_power/cmake) by extension 'python'
[0.438s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/Export/share/slpmu_power/cmake) by extensions ['python_setup_py']
[0.438s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/Export/share/slpmu_power/cmake) by extension 'python_setup_py'
[0.439s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/power_utils.dir) by extensions ['ignore', 'ignore_ament_install']
[0.439s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/power_utils.dir) by extension 'ignore'
[0.439s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/power_utils.dir) by extension 'ignore_ament_install'
[0.439s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/power_utils.dir) by extensions ['colcon_pkg']
[0.439s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/power_utils.dir) by extension 'colcon_pkg'
[0.439s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/power_utils.dir) by extensions ['colcon_meta']
[0.439s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/power_utils.dir) by extension 'colcon_meta'
[0.439s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/power_utils.dir) by extensions ['ros']
[0.439s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/power_utils.dir) by extension 'ros'
[0.439s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/power_utils.dir) by extensions ['cmake', 'python']
[0.439s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/power_utils.dir) by extension 'cmake'
[0.439s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/power_utils.dir) by extension 'python'
[0.439s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/power_utils.dir) by extensions ['python_setup_py']
[0.439s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/power_utils.dir) by extension 'python_setup_py'
[0.439s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/power_utils.dir/bin) by extensions ['ignore', 'ignore_ament_install']
[0.439s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/power_utils.dir/bin) by extension 'ignore'
[0.439s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/power_utils.dir/bin) by extension 'ignore_ament_install'
[0.440s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/power_utils.dir/bin) by extensions ['colcon_pkg']
[0.440s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/power_utils.dir/bin) by extension 'colcon_pkg'
[0.440s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/power_utils.dir/bin) by extensions ['colcon_meta']
[0.440s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/power_utils.dir/bin) by extension 'colcon_meta'
[0.440s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/power_utils.dir/bin) by extensions ['ros']
[0.440s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/power_utils.dir/bin) by extension 'ros'
[0.440s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/power_utils.dir/bin) by extensions ['cmake', 'python']
[0.440s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/power_utils.dir/bin) by extension 'cmake'
[0.440s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/power_utils.dir/bin) by extension 'python'
[0.440s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/power_utils.dir/bin) by extensions ['python_setup_py']
[0.440s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/power_utils.dir/bin) by extension 'python_setup_py'
[0.440s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/slpmu_power.dir) by extensions ['ignore', 'ignore_ament_install']
[0.440s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/slpmu_power.dir) by extension 'ignore'
[0.440s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/slpmu_power.dir) by extension 'ignore_ament_install'
[0.440s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/slpmu_power.dir) by extensions ['colcon_pkg']
[0.440s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/slpmu_power.dir) by extension 'colcon_pkg'
[0.440s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/slpmu_power.dir) by extensions ['colcon_meta']
[0.440s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/slpmu_power.dir) by extension 'colcon_meta'
[0.440s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/slpmu_power.dir) by extensions ['ros']
[0.441s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/slpmu_power.dir) by extension 'ros'
[0.441s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/slpmu_power.dir) by extensions ['cmake', 'python']
[0.441s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/slpmu_power.dir) by extension 'cmake'
[0.441s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/slpmu_power.dir) by extension 'python'
[0.441s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/slpmu_power.dir) by extensions ['python_setup_py']
[0.441s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/slpmu_power.dir) by extension 'python_setup_py'
[0.441s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/slpmu_power.dir/src) by extensions ['ignore', 'ignore_ament_install']
[0.441s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/slpmu_power.dir/src) by extension 'ignore'
[0.441s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/slpmu_power.dir/src) by extension 'ignore_ament_install'
[0.441s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/slpmu_power.dir/src) by extensions ['colcon_pkg']
[0.441s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/slpmu_power.dir/src) by extension 'colcon_pkg'
[0.441s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/slpmu_power.dir/src) by extensions ['colcon_meta']
[0.441s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/slpmu_power.dir/src) by extension 'colcon_meta'
[0.441s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/slpmu_power.dir/src) by extensions ['ros']
[0.441s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/slpmu_power.dir/src) by extension 'ros'
[0.441s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/slpmu_power.dir/src) by extensions ['cmake', 'python']
[0.441s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/slpmu_power.dir/src) by extension 'cmake'
[0.441s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/slpmu_power.dir/src) by extension 'python'
[0.442s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/slpmu_power.dir/src) by extensions ['python_setup_py']
[0.442s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/CMakeFiles/slpmu_power.dir/src) by extension 'python_setup_py'
[0.442s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/cmake) by extensions ['ignore', 'ignore_ament_install']
[0.442s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/cmake) by extension 'ignore'
[0.442s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/cmake) by extension 'ignore_ament_install'
[0.442s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/cmake) by extensions ['colcon_pkg']
[0.442s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/cmake) by extension 'colcon_pkg'
[0.442s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/cmake) by extensions ['colcon_meta']
[0.442s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/cmake) by extension 'colcon_meta'
[0.442s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/cmake) by extensions ['ros']
[0.442s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/cmake) by extension 'ros'
[0.442s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/cmake) by extensions ['cmake', 'python']
[0.442s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/cmake) by extension 'cmake'
[0.442s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/cmake) by extension 'python'
[0.442s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/cmake) by extensions ['python_setup_py']
[0.442s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/cmake) by extension 'python_setup_py'
[0.442s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/cmake/slpmu_power) by extensions ['ignore', 'ignore_ament_install']
[0.443s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/cmake/slpmu_power) by extension 'ignore'
[0.443s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/cmake/slpmu_power) by extension 'ignore_ament_install'
[0.443s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/cmake/slpmu_power) by extensions ['colcon_pkg']
[0.443s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/cmake/slpmu_power) by extension 'colcon_pkg'
[0.443s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/cmake/slpmu_power) by extensions ['colcon_meta']
[0.443s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/cmake/slpmu_power) by extension 'colcon_meta'
[0.443s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/cmake/slpmu_power) by extensions ['ros']
[0.443s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/cmake/slpmu_power) by extension 'ros'
[0.443s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/cmake/slpmu_power) by extensions ['cmake', 'python']
[0.443s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/cmake/slpmu_power) by extension 'cmake'
[0.443s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/cmake/slpmu_power) by extension 'python'
[0.443s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/cmake/slpmu_power) by extensions ['python_setup_py']
[0.443s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_power/cmake/slpmu_power) by extension 'python_setup_py'
[0.443s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process) by extensions ['ignore', 'ignore_ament_install']
[0.443s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process) by extension 'ignore'
[0.443s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process) by extension 'ignore_ament_install'
[0.443s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process) by extensions ['colcon_pkg']
[0.444s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process) by extension 'colcon_pkg'
[0.444s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process) by extensions ['colcon_meta']
[0.444s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process) by extension 'colcon_meta'
[0.444s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process) by extensions ['ros']
[0.444s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process) by extension 'ros'
[0.444s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process) by extensions ['cmake', 'python']
[0.444s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process) by extension 'cmake'
[0.444s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process) by extension 'python'
[0.444s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process) by extensions ['python_setup_py']
[0.444s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process) by extension 'python_setup_py'
[0.444s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles) by extensions ['ignore', 'ignore_ament_install']
[0.444s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles) by extension 'ignore'
[0.444s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles) by extension 'ignore_ament_install'
[0.444s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles) by extensions ['colcon_pkg']
[0.444s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles) by extension 'colcon_pkg'
[0.444s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles) by extensions ['colcon_meta']
[0.444s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles) by extension 'colcon_meta'
[0.444s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles) by extensions ['ros']
[0.444s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles) by extension 'ros'
[0.445s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles) by extensions ['cmake', 'python']
[0.445s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles) by extension 'cmake'
[0.445s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles) by extension 'python'
[0.445s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles) by extensions ['python_setup_py']
[0.445s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles) by extension 'python_setup_py'
[0.445s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/3.22.1) by extensions ['ignore', 'ignore_ament_install']
[0.445s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/3.22.1) by extension 'ignore'
[0.445s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/3.22.1) by extension 'ignore_ament_install'
[0.445s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/3.22.1) by extensions ['colcon_pkg']
[0.445s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/3.22.1) by extension 'colcon_pkg'
[0.445s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/3.22.1) by extensions ['colcon_meta']
[0.445s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/3.22.1) by extension 'colcon_meta'
[0.445s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/3.22.1) by extensions ['ros']
[0.445s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/3.22.1) by extension 'ros'
[0.445s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/3.22.1) by extensions ['cmake', 'python']
[0.445s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/3.22.1) by extension 'cmake'
[0.445s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/3.22.1) by extension 'python'
[0.445s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/3.22.1) by extensions ['python_setup_py']
[0.445s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/3.22.1) by extension 'python_setup_py'
[0.446s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/3.22.1/CompilerIdC) by extensions ['ignore', 'ignore_ament_install']
[0.446s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/3.22.1/CompilerIdC) by extension 'ignore'
[0.446s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/3.22.1/CompilerIdC) by extension 'ignore_ament_install'
[0.446s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/3.22.1/CompilerIdC) by extensions ['colcon_pkg']
[0.446s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/3.22.1/CompilerIdC) by extension 'colcon_pkg'
[0.446s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/3.22.1/CompilerIdC) by extensions ['colcon_meta']
[0.446s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/3.22.1/CompilerIdC) by extension 'colcon_meta'
[0.446s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/3.22.1/CompilerIdC) by extensions ['ros']
[0.446s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/3.22.1/CompilerIdC) by extension 'ros'
[0.446s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/3.22.1/CompilerIdC) by extensions ['cmake', 'python']
[0.446s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/3.22.1/CompilerIdC) by extension 'cmake'
[0.446s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/3.22.1/CompilerIdC) by extension 'python'
[0.446s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/3.22.1/CompilerIdC) by extensions ['python_setup_py']
[0.446s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/3.22.1/CompilerIdC) by extension 'python_setup_py'
[0.446s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/3.22.1/CompilerIdC/tmp) by extensions ['ignore', 'ignore_ament_install']
[0.446s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/3.22.1/CompilerIdC/tmp) by extension 'ignore'
[0.447s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/3.22.1/CompilerIdC/tmp) by extension 'ignore_ament_install'
[0.447s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/3.22.1/CompilerIdC/tmp) by extensions ['colcon_pkg']
[0.447s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/3.22.1/CompilerIdC/tmp) by extension 'colcon_pkg'
[0.447s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/3.22.1/CompilerIdC/tmp) by extensions ['colcon_meta']
[0.447s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/3.22.1/CompilerIdC/tmp) by extension 'colcon_meta'
[0.447s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/3.22.1/CompilerIdC/tmp) by extensions ['ros']
[0.447s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/3.22.1/CompilerIdC/tmp) by extension 'ros'
[0.447s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/3.22.1/CompilerIdC/tmp) by extensions ['cmake', 'python']
[0.447s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/3.22.1/CompilerIdC/tmp) by extension 'cmake'
[0.447s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/3.22.1/CompilerIdC/tmp) by extension 'python'
[0.447s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/3.22.1/CompilerIdC/tmp) by extensions ['python_setup_py']
[0.447s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/3.22.1/CompilerIdC/tmp) by extension 'python_setup_py'
[0.447s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/3.22.1/CompilerIdCXX) by extensions ['ignore', 'ignore_ament_install']
[0.447s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/3.22.1/CompilerIdCXX) by extension 'ignore'
[0.447s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/3.22.1/CompilerIdCXX) by extension 'ignore_ament_install'
[0.447s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/3.22.1/CompilerIdCXX) by extensions ['colcon_pkg']
[0.447s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/3.22.1/CompilerIdCXX) by extension 'colcon_pkg'
[0.447s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/3.22.1/CompilerIdCXX) by extensions ['colcon_meta']
[0.447s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/3.22.1/CompilerIdCXX) by extension 'colcon_meta'
[0.448s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/3.22.1/CompilerIdCXX) by extensions ['ros']
[0.448s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/3.22.1/CompilerIdCXX) by extension 'ros'
[0.448s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/3.22.1/CompilerIdCXX) by extensions ['cmake', 'python']
[0.448s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/3.22.1/CompilerIdCXX) by extension 'cmake'
[0.448s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/3.22.1/CompilerIdCXX) by extension 'python'
[0.448s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/3.22.1/CompilerIdCXX) by extensions ['python_setup_py']
[0.448s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/3.22.1/CompilerIdCXX) by extension 'python_setup_py'
[0.448s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/3.22.1/CompilerIdCXX/tmp) by extensions ['ignore', 'ignore_ament_install']
[0.448s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/3.22.1/CompilerIdCXX/tmp) by extension 'ignore'
[0.448s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/3.22.1/CompilerIdCXX/tmp) by extension 'ignore_ament_install'
[0.448s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/3.22.1/CompilerIdCXX/tmp) by extensions ['colcon_pkg']
[0.448s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/3.22.1/CompilerIdCXX/tmp) by extension 'colcon_pkg'
[0.448s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/3.22.1/CompilerIdCXX/tmp) by extensions ['colcon_meta']
[0.448s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/3.22.1/CompilerIdCXX/tmp) by extension 'colcon_meta'
[0.448s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/3.22.1/CompilerIdCXX/tmp) by extensions ['ros']
[0.448s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/3.22.1/CompilerIdCXX/tmp) by extension 'ros'
[0.448s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/3.22.1/CompilerIdCXX/tmp) by extensions ['cmake', 'python']
[0.448s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/3.22.1/CompilerIdCXX/tmp) by extension 'cmake'
[0.449s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/3.22.1/CompilerIdCXX/tmp) by extension 'python'
[0.449s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/3.22.1/CompilerIdCXX/tmp) by extensions ['python_setup_py']
[0.449s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/3.22.1/CompilerIdCXX/tmp) by extension 'python_setup_py'
[0.449s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/CMakeTmp) by extensions ['ignore', 'ignore_ament_install']
[0.449s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/CMakeTmp) by extension 'ignore'
[0.449s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/CMakeTmp) by extension 'ignore_ament_install'
[0.449s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/CMakeTmp) by extensions ['colcon_pkg']
[0.449s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/CMakeTmp) by extension 'colcon_pkg'
[0.449s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/CMakeTmp) by extensions ['colcon_meta']
[0.449s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/CMakeTmp) by extension 'colcon_meta'
[0.449s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/CMakeTmp) by extensions ['ros']
[0.449s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/CMakeTmp) by extension 'ros'
[0.449s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/CMakeTmp) by extensions ['cmake', 'python']
[0.449s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/CMakeTmp) by extension 'cmake'
[0.449s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/CMakeTmp) by extension 'python'
[0.449s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/CMakeTmp) by extensions ['python_setup_py']
[0.449s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/CMakeTmp) by extension 'python_setup_py'
[0.449s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/Export) by extensions ['ignore', 'ignore_ament_install']
[0.450s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/Export) by extension 'ignore'
[0.450s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/Export) by extension 'ignore_ament_install'
[0.450s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/Export) by extensions ['colcon_pkg']
[0.450s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/Export) by extension 'colcon_pkg'
[0.450s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/Export) by extensions ['colcon_meta']
[0.450s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/Export) by extension 'colcon_meta'
[0.450s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/Export) by extensions ['ros']
[0.450s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/Export) by extension 'ros'
[0.450s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/Export) by extensions ['cmake', 'python']
[0.450s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/Export) by extension 'cmake'
[0.450s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/Export) by extension 'python'
[0.450s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/Export) by extensions ['python_setup_py']
[0.450s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/Export) by extension 'python_setup_py'
[0.450s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/Export/share) by extensions ['ignore', 'ignore_ament_install']
[0.450s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/Export/share) by extension 'ignore'
[0.450s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/Export/share) by extension 'ignore_ament_install'
[0.450s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/Export/share) by extensions ['colcon_pkg']
[0.451s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/Export/share) by extension 'colcon_pkg'
[0.451s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/Export/share) by extensions ['colcon_meta']
[0.451s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/Export/share) by extension 'colcon_meta'
[0.451s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/Export/share) by extensions ['ros']
[0.451s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/Export/share) by extension 'ros'
[0.451s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/Export/share) by extensions ['cmake', 'python']
[0.451s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/Export/share) by extension 'cmake'
[0.451s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/Export/share) by extension 'python'
[0.451s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/Export/share) by extensions ['python_setup_py']
[0.451s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/Export/share) by extension 'python_setup_py'
[0.451s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/Export/share/slpmu_process) by extensions ['ignore', 'ignore_ament_install']
[0.451s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/Export/share/slpmu_process) by extension 'ignore'
[0.451s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/Export/share/slpmu_process) by extension 'ignore_ament_install'
[0.451s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/Export/share/slpmu_process) by extensions ['colcon_pkg']
[0.451s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/Export/share/slpmu_process) by extension 'colcon_pkg'
[0.451s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/Export/share/slpmu_process) by extensions ['colcon_meta']
[0.451s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/Export/share/slpmu_process) by extension 'colcon_meta'
[0.451s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/Export/share/slpmu_process) by extensions ['ros']
[0.451s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/Export/share/slpmu_process) by extension 'ros'
[0.452s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/Export/share/slpmu_process) by extensions ['cmake', 'python']
[0.452s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/Export/share/slpmu_process) by extension 'cmake'
[0.452s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/Export/share/slpmu_process) by extension 'python'
[0.452s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/Export/share/slpmu_process) by extensions ['python_setup_py']
[0.452s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/Export/share/slpmu_process) by extension 'python_setup_py'
[0.452s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/Export/share/slpmu_process/cmake) by extensions ['ignore', 'ignore_ament_install']
[0.452s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/Export/share/slpmu_process/cmake) by extension 'ignore'
[0.452s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/Export/share/slpmu_process/cmake) by extension 'ignore_ament_install'
[0.452s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/Export/share/slpmu_process/cmake) by extensions ['colcon_pkg']
[0.452s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/Export/share/slpmu_process/cmake) by extension 'colcon_pkg'
[0.452s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/Export/share/slpmu_process/cmake) by extensions ['colcon_meta']
[0.452s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/Export/share/slpmu_process/cmake) by extension 'colcon_meta'
[0.452s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/Export/share/slpmu_process/cmake) by extensions ['ros']
[0.452s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/Export/share/slpmu_process/cmake) by extension 'ros'
[0.452s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/Export/share/slpmu_process/cmake) by extensions ['cmake', 'python']
[0.452s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/Export/share/slpmu_process/cmake) by extension 'cmake'
[0.452s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/Export/share/slpmu_process/cmake) by extension 'python'
[0.452s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/Export/share/slpmu_process/cmake) by extensions ['python_setup_py']
[0.453s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/Export/share/slpmu_process/cmake) by extension 'python_setup_py'
[0.453s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/slpmu_process.dir) by extensions ['ignore', 'ignore_ament_install']
[0.453s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/slpmu_process.dir) by extension 'ignore'
[0.453s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/slpmu_process.dir) by extension 'ignore_ament_install'
[0.453s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/slpmu_process.dir) by extensions ['colcon_pkg']
[0.453s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/slpmu_process.dir) by extension 'colcon_pkg'
[0.453s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/slpmu_process.dir) by extensions ['colcon_meta']
[0.453s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/slpmu_process.dir) by extension 'colcon_meta'
[0.453s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/slpmu_process.dir) by extensions ['ros']
[0.453s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/slpmu_process.dir) by extension 'ros'
[0.453s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/slpmu_process.dir) by extensions ['cmake', 'python']
[0.453s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/slpmu_process.dir) by extension 'cmake'
[0.453s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/slpmu_process.dir) by extension 'python'
[0.453s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/slpmu_process.dir) by extensions ['python_setup_py']
[0.453s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/slpmu_process.dir) by extension 'python_setup_py'
[0.453s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/slpmu_process.dir/src) by extensions ['ignore', 'ignore_ament_install']
[0.454s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/slpmu_process.dir/src) by extension 'ignore'
[0.454s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/slpmu_process.dir/src) by extension 'ignore_ament_install'
[0.454s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/slpmu_process.dir/src) by extensions ['colcon_pkg']
[0.454s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/slpmu_process.dir/src) by extension 'colcon_pkg'
[0.454s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/slpmu_process.dir/src) by extensions ['colcon_meta']
[0.454s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/slpmu_process.dir/src) by extension 'colcon_meta'
[0.454s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/slpmu_process.dir/src) by extensions ['ros']
[0.454s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/slpmu_process.dir/src) by extension 'ros'
[0.454s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/slpmu_process.dir/src) by extensions ['cmake', 'python']
[0.454s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/slpmu_process.dir/src) by extension 'cmake'
[0.454s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/slpmu_process.dir/src) by extension 'python'
[0.454s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/slpmu_process.dir/src) by extensions ['python_setup_py']
[0.454s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/CMakeFiles/slpmu_process.dir/src) by extension 'python_setup_py'
[0.454s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/cmake) by extensions ['ignore', 'ignore_ament_install']
[0.454s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/cmake) by extension 'ignore'
[0.454s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/cmake) by extension 'ignore_ament_install'
[0.455s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/cmake) by extensions ['colcon_pkg']
[0.455s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/cmake) by extension 'colcon_pkg'
[0.455s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/cmake) by extensions ['colcon_meta']
[0.455s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/cmake) by extension 'colcon_meta'
[0.455s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/cmake) by extensions ['ros']
[0.455s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/cmake) by extension 'ros'
[0.455s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/cmake) by extensions ['cmake', 'python']
[0.455s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/cmake) by extension 'cmake'
[0.455s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/cmake) by extension 'python'
[0.455s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/cmake) by extensions ['python_setup_py']
[0.455s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/cmake) by extension 'python_setup_py'
[0.455s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/cmake/slpmu_process) by extensions ['ignore', 'ignore_ament_install']
[0.455s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/cmake/slpmu_process) by extension 'ignore'
[0.455s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/cmake/slpmu_process) by extension 'ignore_ament_install'
[0.455s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/cmake/slpmu_process) by extensions ['colcon_pkg']
[0.455s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/cmake/slpmu_process) by extension 'colcon_pkg'
[0.455s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/cmake/slpmu_process) by extensions ['colcon_meta']
[0.455s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/cmake/slpmu_process) by extension 'colcon_meta'
[0.455s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/cmake/slpmu_process) by extensions ['ros']
[0.455s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/cmake/slpmu_process) by extension 'ros'
[0.456s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/cmake/slpmu_process) by extensions ['cmake', 'python']
[0.456s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/cmake/slpmu_process) by extension 'cmake'
[0.456s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/cmake/slpmu_process) by extension 'python'
[0.456s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/cmake/slpmu_process) by extensions ['python_setup_py']
[0.456s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/build/slpmu_process/cmake/slpmu_process) by extension 'python_setup_py'
[0.456s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install) by extensions ['ignore', 'ignore_ament_install']
[0.456s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install) by extension 'ignore'
[0.456s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install) by extension 'ignore_ament_install'
[0.456s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install) by extensions ['colcon_pkg']
[0.456s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install) by extension 'colcon_pkg'
[0.456s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install) by extensions ['colcon_meta']
[0.456s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install) by extension 'colcon_meta'
[0.456s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install) by extensions ['ros']
[0.456s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install) by extension 'ros'
[0.456s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install) by extensions ['cmake', 'python']
[0.456s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install) by extension 'cmake'
[0.456s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install) by extension 'python'
[0.456s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install) by extensions ['python_setup_py']
[0.456s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install) by extension 'python_setup_py'
[0.457s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/bin) by extensions ['ignore', 'ignore_ament_install']
[0.457s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/bin) by extension 'ignore'
[0.457s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/bin) by extension 'ignore_ament_install'
[0.457s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/bin) by extensions ['colcon_pkg']
[0.457s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/bin) by extension 'colcon_pkg'
[0.457s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/bin) by extensions ['colcon_meta']
[0.457s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/bin) by extension 'colcon_meta'
[0.457s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/bin) by extensions ['ros']
[0.457s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/bin) by extension 'ros'
[0.457s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/bin) by extensions ['cmake', 'python']
[0.457s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/bin) by extension 'cmake'
[0.457s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/bin) by extension 'python'
[0.457s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/bin) by extensions ['python_setup_py']
[0.457s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/bin) by extension 'python_setup_py'
[0.457s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include) by extensions ['ignore', 'ignore_ament_install']
[0.457s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include) by extension 'ignore'
[0.457s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include) by extension 'ignore_ament_install'
[0.458s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include) by extensions ['colcon_pkg']
[0.458s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include) by extension 'colcon_pkg'
[0.458s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include) by extensions ['colcon_meta']
[0.458s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include) by extension 'colcon_meta'
[0.458s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include) by extensions ['ros']
[0.458s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include) by extension 'ros'
[0.458s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include) by extensions ['cmake', 'python']
[0.458s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include) by extension 'cmake'
[0.458s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include) by extension 'python'
[0.458s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include) by extensions ['python_setup_py']
[0.458s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include) by extension 'python_setup_py'
[0.458s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slcan) by extensions ['ignore', 'ignore_ament_install']
[0.458s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slcan) by extension 'ignore'
[0.458s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slcan) by extension 'ignore_ament_install'
[0.458s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slcan) by extensions ['colcon_pkg']
[0.458s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slcan) by extension 'colcon_pkg'
[0.458s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slcan) by extensions ['colcon_meta']
[0.458s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slcan) by extension 'colcon_meta'
[0.458s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slcan) by extensions ['ros']
[0.459s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slcan) by extension 'ros'
[0.459s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slcan) by extensions ['cmake', 'python']
[0.459s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slcan) by extension 'cmake'
[0.459s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slcan) by extension 'python'
[0.459s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slcan) by extensions ['python_setup_py']
[0.459s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slcan) by extension 'python_setup_py'
[0.459s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slcan/slcan) by extensions ['ignore', 'ignore_ament_install']
[0.459s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slcan/slcan) by extension 'ignore'
[0.459s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slcan/slcan) by extension 'ignore_ament_install'
[0.459s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slcan/slcan) by extensions ['colcon_pkg']
[0.459s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slcan/slcan) by extension 'colcon_pkg'
[0.459s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slcan/slcan) by extensions ['colcon_meta']
[0.459s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slcan/slcan) by extension 'colcon_meta'
[0.459s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slcan/slcan) by extensions ['ros']
[0.459s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slcan/slcan) by extension 'ros'
[0.459s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slcan/slcan) by extensions ['cmake', 'python']
[0.459s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slcan/slcan) by extension 'cmake'
[0.459s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slcan/slcan) by extension 'python'
[0.459s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slcan/slcan) by extensions ['python_setup_py']
[0.460s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slcan/slcan) by extension 'python_setup_py'
[0.460s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slpmu) by extensions ['ignore', 'ignore_ament_install']
[0.460s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slpmu) by extension 'ignore'
[0.460s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slpmu) by extension 'ignore_ament_install'
[0.460s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slpmu) by extensions ['colcon_pkg']
[0.460s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slpmu) by extension 'colcon_pkg'
[0.460s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slpmu) by extensions ['colcon_meta']
[0.460s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slpmu) by extension 'colcon_meta'
[0.460s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slpmu) by extensions ['ros']
[0.460s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slpmu) by extension 'ros'
[0.460s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slpmu) by extensions ['cmake', 'python']
[0.460s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slpmu) by extension 'cmake'
[0.460s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slpmu) by extension 'python'
[0.460s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slpmu) by extensions ['python_setup_py']
[0.460s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slpmu) by extension 'python_setup_py'
[0.460s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slpmu/jack) by extensions ['ignore', 'ignore_ament_install']
[0.461s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slpmu/jack) by extension 'ignore'
[0.461s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slpmu/jack) by extension 'ignore_ament_install'
[0.461s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slpmu/jack) by extensions ['colcon_pkg']
[0.461s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slpmu/jack) by extension 'colcon_pkg'
[0.461s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slpmu/jack) by extensions ['colcon_meta']
[0.461s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slpmu/jack) by extension 'colcon_meta'
[0.461s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slpmu/jack) by extensions ['ros']
[0.461s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slpmu/jack) by extension 'ros'
[0.461s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slpmu/jack) by extensions ['cmake', 'python']
[0.461s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slpmu/jack) by extension 'cmake'
[0.461s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slpmu/jack) by extension 'python'
[0.461s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slpmu/jack) by extensions ['python_setup_py']
[0.461s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slpmu/jack) by extension 'python_setup_py'
[0.461s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slpmu/motor) by extensions ['ignore', 'ignore_ament_install']
[0.461s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slpmu/motor) by extension 'ignore'
[0.461s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slpmu/motor) by extension 'ignore_ament_install'
[0.461s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slpmu/motor) by extensions ['colcon_pkg']
[0.461s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slpmu/motor) by extension 'colcon_pkg'
[0.461s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slpmu/motor) by extensions ['colcon_meta']
[0.461s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slpmu/motor) by extension 'colcon_meta'
[0.462s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slpmu/motor) by extensions ['ros']
[0.462s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slpmu/motor) by extension 'ros'
[0.462s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slpmu/motor) by extensions ['cmake', 'python']
[0.462s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slpmu/motor) by extension 'cmake'
[0.462s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slpmu/motor) by extension 'python'
[0.462s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slpmu/motor) by extensions ['python_setup_py']
[0.462s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slpmu/motor) by extension 'python_setup_py'
[0.462s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slpmu/power) by extensions ['ignore', 'ignore_ament_install']
[0.462s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slpmu/power) by extension 'ignore'
[0.462s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slpmu/power) by extension 'ignore_ament_install'
[0.462s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slpmu/power) by extensions ['colcon_pkg']
[0.462s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slpmu/power) by extension 'colcon_pkg'
[0.462s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slpmu/power) by extensions ['colcon_meta']
[0.462s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slpmu/power) by extension 'colcon_meta'
[0.462s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slpmu/power) by extensions ['ros']
[0.462s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slpmu/power) by extension 'ros'
[0.462s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slpmu/power) by extensions ['cmake', 'python']
[0.462s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slpmu/power) by extension 'cmake'
[0.463s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slpmu/power) by extension 'python'
[0.463s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slpmu/power) by extensions ['python_setup_py']
[0.463s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slpmu/power) by extension 'python_setup_py'
[0.463s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slpmu/process) by extensions ['ignore', 'ignore_ament_install']
[0.463s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slpmu/process) by extension 'ignore'
[0.463s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slpmu/process) by extension 'ignore_ament_install'
[0.463s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slpmu/process) by extensions ['colcon_pkg']
[0.463s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slpmu/process) by extension 'colcon_pkg'
[0.463s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slpmu/process) by extensions ['colcon_meta']
[0.463s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slpmu/process) by extension 'colcon_meta'
[0.463s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slpmu/process) by extensions ['ros']
[0.463s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slpmu/process) by extension 'ros'
[0.463s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slpmu/process) by extensions ['cmake', 'python']
[0.463s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slpmu/process) by extension 'cmake'
[0.463s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slpmu/process) by extension 'python'
[0.463s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slpmu/process) by extensions ['python_setup_py']
[0.463s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/include/slpmu/process) by extension 'python_setup_py'
[0.463s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/lib) by extensions ['ignore', 'ignore_ament_install']
[0.464s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/lib) by extension 'ignore'
[0.464s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/lib) by extension 'ignore_ament_install'
[0.464s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/lib) by extensions ['colcon_pkg']
[0.464s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/lib) by extension 'colcon_pkg'
[0.464s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/lib) by extensions ['colcon_meta']
[0.464s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/lib) by extension 'colcon_meta'
[0.464s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/lib) by extensions ['ros']
[0.464s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/lib) by extension 'ros'
[0.464s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/lib) by extensions ['cmake', 'python']
[0.464s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/lib) by extension 'cmake'
[0.464s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/lib) by extension 'python'
[0.464s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/lib) by extensions ['python_setup_py']
[0.464s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/lib) by extension 'python_setup_py'
[0.464s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share) by extensions ['ignore', 'ignore_ament_install']
[0.464s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share) by extension 'ignore'
[0.464s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share) by extension 'ignore_ament_install'
[0.464s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share) by extensions ['colcon_pkg']
[0.464s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share) by extension 'colcon_pkg'
[0.465s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share) by extensions ['colcon_meta']
[0.465s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share) by extension 'colcon_meta'
[0.465s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share) by extensions ['ros']
[0.465s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share) by extension 'ros'
[0.465s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share) by extensions ['cmake', 'python']
[0.465s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share) by extension 'cmake'
[0.465s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share) by extension 'python'
[0.465s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share) by extensions ['python_setup_py']
[0.465s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share) by extension 'python_setup_py'
[0.465s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slcan) by extensions ['ignore', 'ignore_ament_install']
[0.465s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slcan) by extension 'ignore'
[0.465s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slcan) by extension 'ignore_ament_install'
[0.465s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slcan) by extensions ['colcon_pkg']
[0.465s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slcan) by extension 'colcon_pkg'
[0.465s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slcan) by extensions ['colcon_meta']
[0.465s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slcan) by extension 'colcon_meta'
[0.465s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slcan) by extensions ['ros']
[0.465s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slcan) by extension 'ros'
[0.465s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slcan) by extensions ['cmake', 'python']
[0.466s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slcan) by extension 'cmake'
[0.466s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slcan) by extension 'python'
[0.466s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slcan) by extensions ['python_setup_py']
[0.466s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slcan) by extension 'python_setup_py'
[0.466s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slcan/cmake) by extensions ['ignore', 'ignore_ament_install']
[0.466s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slcan/cmake) by extension 'ignore'
[0.466s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slcan/cmake) by extension 'ignore_ament_install'
[0.466s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slcan/cmake) by extensions ['colcon_pkg']
[0.466s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slcan/cmake) by extension 'colcon_pkg'
[0.466s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slcan/cmake) by extensions ['colcon_meta']
[0.466s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slcan/cmake) by extension 'colcon_meta'
[0.466s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slcan/cmake) by extensions ['ros']
[0.466s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slcan/cmake) by extension 'ros'
[0.466s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slcan/cmake) by extensions ['cmake', 'python']
[0.466s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slcan/cmake) by extension 'cmake'
[0.466s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slcan/cmake) by extension 'python'
[0.466s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slcan/cmake) by extensions ['python_setup_py']
[0.466s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slcan/cmake) by extension 'python_setup_py'
[0.467s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_jack) by extensions ['ignore', 'ignore_ament_install']
[0.467s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_jack) by extension 'ignore'
[0.467s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_jack) by extension 'ignore_ament_install'
[0.467s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_jack) by extensions ['colcon_pkg']
[0.467s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_jack) by extension 'colcon_pkg'
[0.467s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_jack) by extensions ['colcon_meta']
[0.467s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_jack) by extension 'colcon_meta'
[0.467s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_jack) by extensions ['ros']
[0.467s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_jack) by extension 'ros'
[0.467s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_jack) by extensions ['cmake', 'python']
[0.467s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_jack) by extension 'cmake'
[0.467s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_jack) by extension 'python'
[0.467s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_jack) by extensions ['python_setup_py']
[0.467s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_jack) by extension 'python_setup_py'
[0.467s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_jack/cmake) by extensions ['ignore', 'ignore_ament_install']
[0.467s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_jack/cmake) by extension 'ignore'
[0.467s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_jack/cmake) by extension 'ignore_ament_install'
[0.468s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_jack/cmake) by extensions ['colcon_pkg']
[0.468s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_jack/cmake) by extension 'colcon_pkg'
[0.468s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_jack/cmake) by extensions ['colcon_meta']
[0.468s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_jack/cmake) by extension 'colcon_meta'
[0.468s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_jack/cmake) by extensions ['ros']
[0.468s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_jack/cmake) by extension 'ros'
[0.468s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_jack/cmake) by extensions ['cmake', 'python']
[0.468s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_jack/cmake) by extension 'cmake'
[0.468s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_jack/cmake) by extension 'python'
[0.468s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_jack/cmake) by extensions ['python_setup_py']
[0.468s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_jack/cmake) by extension 'python_setup_py'
[0.468s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_motor) by extensions ['ignore', 'ignore_ament_install']
[0.468s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_motor) by extension 'ignore'
[0.468s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_motor) by extension 'ignore_ament_install'
[0.468s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_motor) by extensions ['colcon_pkg']
[0.468s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_motor) by extension 'colcon_pkg'
[0.468s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_motor) by extensions ['colcon_meta']
[0.468s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_motor) by extension 'colcon_meta'
[0.468s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_motor) by extensions ['ros']
[0.468s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_motor) by extension 'ros'
[0.469s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_motor) by extensions ['cmake', 'python']
[0.469s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_motor) by extension 'cmake'
[0.469s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_motor) by extension 'python'
[0.469s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_motor) by extensions ['python_setup_py']
[0.469s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_motor) by extension 'python_setup_py'
[0.469s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_motor/cmake) by extensions ['ignore', 'ignore_ament_install']
[0.469s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_motor/cmake) by extension 'ignore'
[0.469s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_motor/cmake) by extension 'ignore_ament_install'
[0.469s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_motor/cmake) by extensions ['colcon_pkg']
[0.469s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_motor/cmake) by extension 'colcon_pkg'
[0.469s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_motor/cmake) by extensions ['colcon_meta']
[0.469s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_motor/cmake) by extension 'colcon_meta'
[0.469s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_motor/cmake) by extensions ['ros']
[0.469s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_motor/cmake) by extension 'ros'
[0.469s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_motor/cmake) by extensions ['cmake', 'python']
[0.469s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_motor/cmake) by extension 'cmake'
[0.469s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_motor/cmake) by extension 'python'
[0.469s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_motor/cmake) by extensions ['python_setup_py']
[0.469s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_motor/cmake) by extension 'python_setup_py'
[0.470s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_power) by extensions ['ignore', 'ignore_ament_install']
[0.470s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_power) by extension 'ignore'
[0.470s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_power) by extension 'ignore_ament_install'
[0.470s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_power) by extensions ['colcon_pkg']
[0.470s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_power) by extension 'colcon_pkg'
[0.470s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_power) by extensions ['colcon_meta']
[0.470s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_power) by extension 'colcon_meta'
[0.470s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_power) by extensions ['ros']
[0.470s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_power) by extension 'ros'
[0.470s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_power) by extensions ['cmake', 'python']
[0.470s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_power) by extension 'cmake'
[0.470s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_power) by extension 'python'
[0.470s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_power) by extensions ['python_setup_py']
[0.470s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_power) by extension 'python_setup_py'
[0.470s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_power/cmake) by extensions ['ignore', 'ignore_ament_install']
[0.470s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_power/cmake) by extension 'ignore'
[0.471s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_power/cmake) by extension 'ignore_ament_install'
[0.471s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_power/cmake) by extensions ['colcon_pkg']
[0.471s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_power/cmake) by extension 'colcon_pkg'
[0.471s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_power/cmake) by extensions ['colcon_meta']
[0.471s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_power/cmake) by extension 'colcon_meta'
[0.471s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_power/cmake) by extensions ['ros']
[0.471s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_power/cmake) by extension 'ros'
[0.471s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_power/cmake) by extensions ['cmake', 'python']
[0.471s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_power/cmake) by extension 'cmake'
[0.471s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_power/cmake) by extension 'python'
[0.471s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_power/cmake) by extensions ['python_setup_py']
[0.471s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_power/cmake) by extension 'python_setup_py'
[0.471s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_process) by extensions ['ignore', 'ignore_ament_install']
[0.471s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_process) by extension 'ignore'
[0.471s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_process) by extension 'ignore_ament_install'
[0.471s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_process) by extensions ['colcon_pkg']
[0.471s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_process) by extension 'colcon_pkg'
[0.471s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_process) by extensions ['colcon_meta']
[0.471s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_process) by extension 'colcon_meta'
[0.471s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_process) by extensions ['ros']
[0.472s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_process) by extension 'ros'
[0.472s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_process) by extensions ['cmake', 'python']
[0.472s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_process) by extension 'cmake'
[0.472s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_process) by extension 'python'
[0.472s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_process) by extensions ['python_setup_py']
[0.472s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_process) by extension 'python_setup_py'
[0.472s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_process/cmake) by extensions ['ignore', 'ignore_ament_install']
[0.472s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_process/cmake) by extension 'ignore'
[0.472s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_process/cmake) by extension 'ignore_ament_install'
[0.472s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_process/cmake) by extensions ['colcon_pkg']
[0.472s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_process/cmake) by extension 'colcon_pkg'
[0.472s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_process/cmake) by extensions ['colcon_meta']
[0.472s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_process/cmake) by extension 'colcon_meta'
[0.472s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_process/cmake) by extensions ['ros']
[0.472s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_process/cmake) by extension 'ros'
[0.472s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_process/cmake) by extensions ['cmake', 'python']
[0.472s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_process/cmake) by extension 'cmake'
[0.472s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_process/cmake) by extension 'python'
[0.473s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_process/cmake) by extensions ['python_setup_py']
[0.473s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/install/share/slpmu_process/cmake) by extension 'python_setup_py'
[0.473s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/scripts) by extensions ['ignore', 'ignore_ament_install']
[0.473s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/scripts) by extension 'ignore'
[0.473s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/scripts) by extension 'ignore_ament_install'
[0.473s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/scripts) by extensions ['colcon_pkg']
[0.473s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/scripts) by extension 'colcon_pkg'
[0.473s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/scripts) by extensions ['colcon_meta']
[0.473s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/scripts) by extension 'colcon_meta'
[0.473s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/scripts) by extensions ['ros']
[0.473s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/scripts) by extension 'ros'
[0.473s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/scripts) by extensions ['cmake', 'python']
[0.473s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/scripts) by extension 'cmake'
[0.473s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/scripts) by extension 'python'
[0.473s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/scripts) by extensions ['python_setup_py']
[0.473s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/scripts) by extension 'python_setup_py'
[0.473s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slcan) by extensions ['ignore', 'ignore_ament_install']
[0.473s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slcan) by extension 'ignore'
[0.473s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slcan) by extension 'ignore_ament_install'
[0.474s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slcan) by extensions ['colcon_pkg']
[0.474s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slcan) by extension 'colcon_pkg'
[0.474s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slcan) by extensions ['colcon_meta']
[0.474s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slcan) by extension 'colcon_meta'
[0.474s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slcan) by extensions ['ros']
[0.474s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slcan) by extension 'ros'
[0.478s] DEBUG:colcon.colcon_core.package_identification:Package 'src/pmu/slcan' with type 'ros.cmake' and name 'slcan'
[0.478s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_jack) by extensions ['ignore', 'ignore_ament_install']
[0.478s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_jack) by extension 'ignore'
[0.478s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_jack) by extension 'ignore_ament_install'
[0.478s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_jack) by extensions ['colcon_pkg']
[0.478s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_jack) by extension 'colcon_pkg'
[0.478s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_jack) by extensions ['colcon_meta']
[0.478s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_jack) by extension 'colcon_meta'
[0.478s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_jack) by extensions ['ros']
[0.478s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_jack) by extension 'ros'
[0.479s] DEBUG:colcon.colcon_core.package_identification:Package 'src/pmu/slpmu_jack' with type 'ros.cmake' and name 'slpmu_jack'
[0.479s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_motor) by extensions ['ignore', 'ignore_ament_install']
[0.479s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_motor) by extension 'ignore'
[0.479s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_motor) by extension 'ignore_ament_install'
[0.480s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_motor) by extensions ['colcon_pkg']
[0.480s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_motor) by extension 'colcon_pkg'
[0.480s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_motor) by extensions ['colcon_meta']
[0.480s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_motor) by extension 'colcon_meta'
[0.480s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_motor) by extensions ['ros']
[0.480s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_motor) by extension 'ros'
[0.480s] DEBUG:colcon.colcon_core.package_identification:Package 'src/pmu/slpmu_motor' with type 'ros.cmake' and name 'slpmu_motor'
[0.481s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_power) by extensions ['ignore', 'ignore_ament_install']
[0.481s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_power) by extension 'ignore'
[0.481s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_power) by extension 'ignore_ament_install'
[0.481s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_power) by extensions ['colcon_pkg']
[0.481s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_power) by extension 'colcon_pkg'
[0.481s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_power) by extensions ['colcon_meta']
[0.481s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_power) by extension 'colcon_meta'
[0.481s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_power) by extensions ['ros']
[0.481s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_power) by extension 'ros'
[0.482s] DEBUG:colcon.colcon_core.package_identification:Package 'src/pmu/slpmu_power' with type 'ros.cmake' and name 'slpmu_power'
[0.482s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_process) by extensions ['ignore', 'ignore_ament_install']
[0.482s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_process) by extension 'ignore'
[0.482s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_process) by extension 'ignore_ament_install'
[0.482s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_process) by extensions ['colcon_pkg']
[0.482s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_process) by extension 'colcon_pkg'
[0.482s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_process) by extensions ['colcon_meta']
[0.482s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_process) by extension 'colcon_meta'
[0.482s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_process) by extensions ['ros']
[0.482s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_process) by extension 'ros'
[0.483s] DEBUG:colcon.colcon_core.package_identification:Package 'src/pmu/slpmu_process' with type 'ros.cmake' and name 'slpmu_process'
[0.483s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_ros2) by extensions ['ignore', 'ignore_ament_install']
[0.483s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_ros2) by extension 'ignore'
[0.483s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_ros2) by extension 'ignore_ament_install'
[0.483s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_ros2) by extensions ['colcon_pkg']
[0.483s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_ros2) by extension 'colcon_pkg'
[0.483s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_ros2) by extensions ['colcon_meta']
[0.483s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_ros2) by extension 'colcon_meta'
[0.483s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_ros2) by extensions ['ros']
[0.483s] Level 1:colcon.colcon_core.package_identification:_identify(src/pmu/slpmu_ros2) by extension 'ros'
[0.485s] DEBUG:colcon.colcon_core.package_identification:Package 'src/pmu/slpmu_ros2' with type 'ros.ament_cmake' and name 'slpmu_ros2'
[0.485s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.485s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.485s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.485s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.485s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.554s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.555s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.557s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 6 installed packages in /home/<USER>/yuan/amr_ws/install
[0.558s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 282 installed packages in /opt/ros/humble
[0.560s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.614s] Level 5:colcon.colcon_core.verb:set package 'slcan' build argument 'cmake_args' from command line to 'None'
[0.614s] Level 5:colcon.colcon_core.verb:set package 'slcan' build argument 'cmake_target' from command line to 'None'
[0.614s] Level 5:colcon.colcon_core.verb:set package 'slcan' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.614s] Level 5:colcon.colcon_core.verb:set package 'slcan' build argument 'cmake_clean_cache' from command line to 'False'
[0.614s] Level 5:colcon.colcon_core.verb:set package 'slcan' build argument 'cmake_clean_first' from command line to 'False'
[0.614s] Level 5:colcon.colcon_core.verb:set package 'slcan' build argument 'cmake_force_configure' from command line to 'False'
[0.614s] Level 5:colcon.colcon_core.verb:set package 'slcan' build argument 'ament_cmake_args' from command line to 'None'
[0.614s] Level 5:colcon.colcon_core.verb:set package 'slcan' build argument 'catkin_cmake_args' from command line to 'None'
[0.614s] Level 5:colcon.colcon_core.verb:set package 'slcan' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.614s] DEBUG:colcon.colcon_core.verb:Building package 'slcan' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/yuan/amr_ws/build/slcan', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/yuan/amr_ws/install/slcan', 'merge_install': False, 'path': '/home/<USER>/yuan/amr_ws/src/pmu/slcan', 'symlink_install': False, 'test_result_base': None}
[0.615s] Level 5:colcon.colcon_core.verb:set package 'slpmu_process' build argument 'cmake_args' from command line to 'None'
[0.615s] Level 5:colcon.colcon_core.verb:set package 'slpmu_process' build argument 'cmake_target' from command line to 'None'
[0.615s] Level 5:colcon.colcon_core.verb:set package 'slpmu_process' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.615s] Level 5:colcon.colcon_core.verb:set package 'slpmu_process' build argument 'cmake_clean_cache' from command line to 'False'
[0.615s] Level 5:colcon.colcon_core.verb:set package 'slpmu_process' build argument 'cmake_clean_first' from command line to 'False'
[0.615s] Level 5:colcon.colcon_core.verb:set package 'slpmu_process' build argument 'cmake_force_configure' from command line to 'False'
[0.615s] Level 5:colcon.colcon_core.verb:set package 'slpmu_process' build argument 'ament_cmake_args' from command line to 'None'
[0.615s] Level 5:colcon.colcon_core.verb:set package 'slpmu_process' build argument 'catkin_cmake_args' from command line to 'None'
[0.615s] Level 5:colcon.colcon_core.verb:set package 'slpmu_process' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.615s] DEBUG:colcon.colcon_core.verb:Building package 'slpmu_process' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/yuan/amr_ws/build/slpmu_process', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/yuan/amr_ws/install/slpmu_process', 'merge_install': False, 'path': '/home/<USER>/yuan/amr_ws/src/pmu/slpmu_process', 'symlink_install': False, 'test_result_base': None}
[0.615s] Level 5:colcon.colcon_core.verb:set package 'slpmu_jack' build argument 'cmake_args' from command line to 'None'
[0.615s] Level 5:colcon.colcon_core.verb:set package 'slpmu_jack' build argument 'cmake_target' from command line to 'None'
[0.615s] Level 5:colcon.colcon_core.verb:set package 'slpmu_jack' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.615s] Level 5:colcon.colcon_core.verb:set package 'slpmu_jack' build argument 'cmake_clean_cache' from command line to 'False'
[0.615s] Level 5:colcon.colcon_core.verb:set package 'slpmu_jack' build argument 'cmake_clean_first' from command line to 'False'
[0.615s] Level 5:colcon.colcon_core.verb:set package 'slpmu_jack' build argument 'cmake_force_configure' from command line to 'False'
[0.615s] Level 5:colcon.colcon_core.verb:set package 'slpmu_jack' build argument 'ament_cmake_args' from command line to 'None'
[0.615s] Level 5:colcon.colcon_core.verb:set package 'slpmu_jack' build argument 'catkin_cmake_args' from command line to 'None'
[0.616s] Level 5:colcon.colcon_core.verb:set package 'slpmu_jack' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.616s] DEBUG:colcon.colcon_core.verb:Building package 'slpmu_jack' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/yuan/amr_ws/build/slpmu_jack', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/yuan/amr_ws/install/slpmu_jack', 'merge_install': False, 'path': '/home/<USER>/yuan/amr_ws/src/pmu/slpmu_jack', 'symlink_install': False, 'test_result_base': None}
[0.616s] Level 5:colcon.colcon_core.verb:set package 'slpmu_motor' build argument 'cmake_args' from command line to 'None'
[0.616s] Level 5:colcon.colcon_core.verb:set package 'slpmu_motor' build argument 'cmake_target' from command line to 'None'
[0.616s] Level 5:colcon.colcon_core.verb:set package 'slpmu_motor' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.616s] Level 5:colcon.colcon_core.verb:set package 'slpmu_motor' build argument 'cmake_clean_cache' from command line to 'False'
[0.616s] Level 5:colcon.colcon_core.verb:set package 'slpmu_motor' build argument 'cmake_clean_first' from command line to 'False'
[0.616s] Level 5:colcon.colcon_core.verb:set package 'slpmu_motor' build argument 'cmake_force_configure' from command line to 'False'
[0.616s] Level 5:colcon.colcon_core.verb:set package 'slpmu_motor' build argument 'ament_cmake_args' from command line to 'None'
[0.616s] Level 5:colcon.colcon_core.verb:set package 'slpmu_motor' build argument 'catkin_cmake_args' from command line to 'None'
[0.616s] Level 5:colcon.colcon_core.verb:set package 'slpmu_motor' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.616s] DEBUG:colcon.colcon_core.verb:Building package 'slpmu_motor' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/yuan/amr_ws/build/slpmu_motor', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/yuan/amr_ws/install/slpmu_motor', 'merge_install': False, 'path': '/home/<USER>/yuan/amr_ws/src/pmu/slpmu_motor', 'symlink_install': False, 'test_result_base': None}
[0.617s] Level 5:colcon.colcon_core.verb:set package 'slpmu_power' build argument 'cmake_args' from command line to 'None'
[0.617s] Level 5:colcon.colcon_core.verb:set package 'slpmu_power' build argument 'cmake_target' from command line to 'None'
[0.617s] Level 5:colcon.colcon_core.verb:set package 'slpmu_power' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.617s] Level 5:colcon.colcon_core.verb:set package 'slpmu_power' build argument 'cmake_clean_cache' from command line to 'False'
[0.617s] Level 5:colcon.colcon_core.verb:set package 'slpmu_power' build argument 'cmake_clean_first' from command line to 'False'
[0.617s] Level 5:colcon.colcon_core.verb:set package 'slpmu_power' build argument 'cmake_force_configure' from command line to 'False'
[0.617s] Level 5:colcon.colcon_core.verb:set package 'slpmu_power' build argument 'ament_cmake_args' from command line to 'None'
[0.617s] Level 5:colcon.colcon_core.verb:set package 'slpmu_power' build argument 'catkin_cmake_args' from command line to 'None'
[0.617s] Level 5:colcon.colcon_core.verb:set package 'slpmu_power' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.617s] DEBUG:colcon.colcon_core.verb:Building package 'slpmu_power' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/yuan/amr_ws/build/slpmu_power', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/yuan/amr_ws/install/slpmu_power', 'merge_install': False, 'path': '/home/<USER>/yuan/amr_ws/src/pmu/slpmu_power', 'symlink_install': False, 'test_result_base': None}
[0.617s] Level 5:colcon.colcon_core.verb:set package 'slpmu_ros2' build argument 'cmake_args' from command line to 'None'
[0.617s] Level 5:colcon.colcon_core.verb:set package 'slpmu_ros2' build argument 'cmake_target' from command line to 'None'
[0.617s] Level 5:colcon.colcon_core.verb:set package 'slpmu_ros2' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.617s] Level 5:colcon.colcon_core.verb:set package 'slpmu_ros2' build argument 'cmake_clean_cache' from command line to 'False'
[0.617s] Level 5:colcon.colcon_core.verb:set package 'slpmu_ros2' build argument 'cmake_clean_first' from command line to 'False'
[0.617s] Level 5:colcon.colcon_core.verb:set package 'slpmu_ros2' build argument 'cmake_force_configure' from command line to 'False'
[0.617s] Level 5:colcon.colcon_core.verb:set package 'slpmu_ros2' build argument 'ament_cmake_args' from command line to 'None'
[0.617s] Level 5:colcon.colcon_core.verb:set package 'slpmu_ros2' build argument 'catkin_cmake_args' from command line to 'None'
[0.617s] Level 5:colcon.colcon_core.verb:set package 'slpmu_ros2' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.617s] DEBUG:colcon.colcon_core.verb:Building package 'slpmu_ros2' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/yuan/amr_ws/build/slpmu_ros2', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/yuan/amr_ws/install/slpmu_ros2', 'merge_install': False, 'path': '/home/<USER>/yuan/amr_ws/src/pmu/slpmu_ros2', 'symlink_install': False, 'test_result_base': None}
[0.618s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.620s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.620s] INFO:colcon.colcon_ros.task.cmake.build:Building ROS package in '/home/<USER>/yuan/amr_ws/src/pmu/slcan' with build type 'cmake'
[0.620s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/yuan/amr_ws/src/pmu/slcan'
[0.624s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.625s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.625s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.630s] INFO:colcon.colcon_ros.task.cmake.build:Building ROS package in '/home/<USER>/yuan/amr_ws/src/pmu/slpmu_process' with build type 'cmake'
[0.630s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/yuan/amr_ws/src/pmu/slpmu_process'
[0.630s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.630s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.646s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/yuan/amr_ws/build/slcan': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/yuan/amr_ws/build/slcan -- -j8 -l8
[0.651s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/yuan/amr_ws/build/slpmu_process': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/yuan/amr_ws/build/slpmu_process -- -j8 -l8
[0.809s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/yuan/amr_ws/build/slpmu_process' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/yuan/amr_ws/build/slpmu_process -- -j8 -l8
[0.850s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/yuan/amr_ws/build/slpmu_process': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/yuan/amr_ws/build/slpmu_process
[0.856s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/yuan/amr_ws/build/slcan' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --build /home/<USER>/yuan/amr_ws/build/slcan -- -j8 -l8
[0.864s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/yuan/amr_ws/build/slcan': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/yuan/amr_ws/build/slcan
[0.880s] Level 1:colcon.colcon_core.shell:create_environment_hook('slcan', 'pkg_config_path')
[0.881s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/yuan/amr_ws/install/slcan/share/slcan/hook/pkg_config_path.ps1'
[0.886s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/yuan/amr_ws/install/slcan/share/slcan/hook/pkg_config_path.dsv'
[0.889s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/yuan/amr_ws/install/slcan/share/slcan/hook/pkg_config_path.sh'
[0.896s] Level 1:colcon.colcon_core.shell:create_environment_hook('slcan', 'pkg_config_path_multiarch')
[0.897s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/yuan/amr_ws/install/slcan/share/slcan/hook/pkg_config_path_multiarch.ps1'
[0.900s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/yuan/amr_ws/install/slcan/share/slcan/hook/pkg_config_path_multiarch.dsv'
[0.900s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/yuan/amr_ws/build/slcan' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/yuan/amr_ws/build/slcan
[0.901s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/yuan/amr_ws/install/slcan/share/slcan/hook/pkg_config_path_multiarch.sh'
[0.901s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(slcan)
[0.904s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/yuan/amr_ws/install/slcan' for CMake module files
[0.905s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/yuan/amr_ws/install/slcan' for CMake config files
[0.905s] Level 1:colcon.colcon_core.shell:create_environment_hook('slcan', 'cmake_prefix_path')
[0.906s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/yuan/amr_ws/install/slcan/share/slcan/hook/cmake_prefix_path.ps1'
[0.906s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/yuan/amr_ws/install/slcan/share/slcan/hook/cmake_prefix_path.dsv'
[0.907s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/yuan/amr_ws/install/slcan/share/slcan/hook/cmake_prefix_path.sh'
[0.908s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/yuan/amr_ws/install/slcan/lib'
[0.908s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/yuan/amr_ws/install/slcan/bin'
[0.908s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/yuan/amr_ws/install/slcan/lib/pkgconfig/slcan.pc'
[0.909s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/yuan/amr_ws/install/slcan/lib/python3.10/site-packages'
[0.909s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/yuan/amr_ws/install/slcan/bin'
[0.910s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/yuan/amr_ws/install/slcan/share/slcan/package.ps1'
[0.911s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/yuan/amr_ws/install/slcan/share/slcan/package.dsv'
[0.912s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/yuan/amr_ws/install/slcan/share/slcan/package.sh'
[0.913s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/yuan/amr_ws/install/slcan/share/slcan/package.bash'
[0.914s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/yuan/amr_ws/install/slcan/share/slcan/package.zsh'
[0.915s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/yuan/amr_ws/install/slcan/share/colcon-core/packages/slcan)
[0.916s] INFO:colcon.colcon_ros.task.cmake.build:Building ROS package in '/home/<USER>/yuan/amr_ws/src/pmu/slpmu_jack' with build type 'cmake'
[0.916s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/yuan/amr_ws/src/pmu/slpmu_jack'
[0.917s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.917s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.922s] INFO:colcon.colcon_ros.task.cmake.build:Building ROS package in '/home/<USER>/yuan/amr_ws/src/pmu/slpmu_motor' with build type 'cmake'
[0.922s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/yuan/amr_ws/src/pmu/slpmu_motor'
[0.923s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.923s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.927s] INFO:colcon.colcon_ros.task.cmake.build:Building ROS package in '/home/<USER>/yuan/amr_ws/src/pmu/slpmu_power' with build type 'cmake'
[0.927s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/yuan/amr_ws/src/pmu/slpmu_power'
[0.928s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.928s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.934s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_process', 'pkg_config_path')
[0.935s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/yuan/amr_ws/install/slpmu_process/share/slpmu_process/hook/pkg_config_path.ps1'
[0.937s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/yuan/amr_ws/build/slpmu_process' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/humble /usr/bin/cmake --install /home/<USER>/yuan/amr_ws/build/slpmu_process
[0.937s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/yuan/amr_ws/install/slpmu_process/share/slpmu_process/hook/pkg_config_path.dsv'
[0.938s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/yuan/amr_ws/install/slpmu_process/share/slpmu_process/hook/pkg_config_path.sh'
[0.938s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_process', 'pkg_config_path_multiarch')
[0.939s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/yuan/amr_ws/install/slpmu_process/share/slpmu_process/hook/pkg_config_path_multiarch.ps1'
[0.940s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/yuan/amr_ws/install/slpmu_process/share/slpmu_process/hook/pkg_config_path_multiarch.dsv'
[0.940s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/yuan/amr_ws/install/slpmu_process/share/slpmu_process/hook/pkg_config_path_multiarch.sh'
[0.940s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(slpmu_process)
[0.941s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/yuan/amr_ws/install/slpmu_process' for CMake module files
[0.941s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/yuan/amr_ws/install/slpmu_process' for CMake config files
[0.941s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_process', 'cmake_prefix_path')
[0.942s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/yuan/amr_ws/install/slpmu_process/share/slpmu_process/hook/cmake_prefix_path.ps1'
[0.942s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/yuan/amr_ws/install/slpmu_process/share/slpmu_process/hook/cmake_prefix_path.dsv'
[0.943s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/yuan/amr_ws/install/slpmu_process/share/slpmu_process/hook/cmake_prefix_path.sh'
[0.943s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/yuan/amr_ws/install/slpmu_process/lib'
[0.944s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/yuan/amr_ws/install/slpmu_process/bin'
[0.944s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/yuan/amr_ws/install/slpmu_process/lib/pkgconfig/slpmu_process.pc'
[0.944s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/yuan/amr_ws/install/slpmu_process/lib/python3.10/site-packages'
[0.944s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/yuan/amr_ws/install/slpmu_process/bin'
[0.945s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/yuan/amr_ws/install/slpmu_process/share/slpmu_process/package.ps1'
[0.946s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/yuan/amr_ws/install/slpmu_process/share/slpmu_process/package.dsv'
[0.946s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/yuan/amr_ws/install/slpmu_process/share/slpmu_process/package.sh'
[0.947s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/yuan/amr_ws/install/slpmu_process/share/slpmu_process/package.bash'
[0.947s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/yuan/amr_ws/install/slpmu_process/share/slpmu_process/package.zsh'
[0.948s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/yuan/amr_ws/install/slpmu_process/share/colcon-core/packages/slpmu_process)
[0.957s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/yuan/amr_ws/build/slpmu_jack': CMAKE_PREFIX_PATH=/home/<USER>/yuan/amr_ws/install/slcan:/home/<USER>/yuan/amr_ws/install/slpmu_ros2:/home/<USER>/yuan/amr_ws/install/slpmu_process:/home/<USER>/yuan/amr_ws/install/slpmu_power:/home/<USER>/yuan/amr_ws/install/slpmu_motor:/home/<USER>/yuan/amr_ws/install/slpmu_jack:/opt/ros/humble PKG_CONFIG_PATH=/home/<USER>/yuan/amr_ws/install/slcan/lib/aarch64-linux-gnu/pkgconfig:/home/<USER>/yuan/amr_ws/install/slcan/lib/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_process/lib/aarch64-linux-gnu/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_process/lib/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_power/lib/aarch64-linux-gnu/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_power/lib/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_motor/lib/aarch64-linux-gnu/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_motor/lib/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_jack/lib/aarch64-linux-gnu/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_jack/lib/pkgconfig /usr/bin/cmake --build /home/<USER>/yuan/amr_ws/build/slpmu_jack -- -j8 -l8
[0.963s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/yuan/amr_ws/build/slpmu_motor': CMAKE_PREFIX_PATH=/home/<USER>/yuan/amr_ws/install/slcan:/home/<USER>/yuan/amr_ws/install/slpmu_ros2:/home/<USER>/yuan/amr_ws/install/slpmu_process:/home/<USER>/yuan/amr_ws/install/slpmu_power:/home/<USER>/yuan/amr_ws/install/slpmu_motor:/home/<USER>/yuan/amr_ws/install/slpmu_jack:/opt/ros/humble PKG_CONFIG_PATH=/home/<USER>/yuan/amr_ws/install/slcan/lib/aarch64-linux-gnu/pkgconfig:/home/<USER>/yuan/amr_ws/install/slcan/lib/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_process/lib/aarch64-linux-gnu/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_process/lib/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_power/lib/aarch64-linux-gnu/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_power/lib/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_motor/lib/aarch64-linux-gnu/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_motor/lib/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_jack/lib/aarch64-linux-gnu/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_jack/lib/pkgconfig /usr/bin/cmake --build /home/<USER>/yuan/amr_ws/build/slpmu_motor -- -j8 -l8
[0.970s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/yuan/amr_ws/build/slpmu_power': CMAKE_PREFIX_PATH=/home/<USER>/yuan/amr_ws/install/slcan:/home/<USER>/yuan/amr_ws/install/slpmu_ros2:/home/<USER>/yuan/amr_ws/install/slpmu_process:/home/<USER>/yuan/amr_ws/install/slpmu_power:/home/<USER>/yuan/amr_ws/install/slpmu_motor:/home/<USER>/yuan/amr_ws/install/slpmu_jack:/opt/ros/humble PKG_CONFIG_PATH=/home/<USER>/yuan/amr_ws/install/slcan/lib/aarch64-linux-gnu/pkgconfig:/home/<USER>/yuan/amr_ws/install/slcan/lib/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_process/lib/aarch64-linux-gnu/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_process/lib/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_power/lib/aarch64-linux-gnu/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_power/lib/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_motor/lib/aarch64-linux-gnu/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_motor/lib/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_jack/lib/aarch64-linux-gnu/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_jack/lib/pkgconfig /usr/bin/cmake --build /home/<USER>/yuan/amr_ws/build/slpmu_power -- -j8 -l8
[1.211s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/yuan/amr_ws/build/slpmu_power' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/yuan/amr_ws/install/slcan:/home/<USER>/yuan/amr_ws/install/slpmu_ros2:/home/<USER>/yuan/amr_ws/install/slpmu_process:/home/<USER>/yuan/amr_ws/install/slpmu_power:/home/<USER>/yuan/amr_ws/install/slpmu_motor:/home/<USER>/yuan/amr_ws/install/slpmu_jack:/opt/ros/humble PKG_CONFIG_PATH=/home/<USER>/yuan/amr_ws/install/slcan/lib/aarch64-linux-gnu/pkgconfig:/home/<USER>/yuan/amr_ws/install/slcan/lib/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_process/lib/aarch64-linux-gnu/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_process/lib/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_power/lib/aarch64-linux-gnu/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_power/lib/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_motor/lib/aarch64-linux-gnu/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_motor/lib/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_jack/lib/aarch64-linux-gnu/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_jack/lib/pkgconfig /usr/bin/cmake --build /home/<USER>/yuan/amr_ws/build/slpmu_power -- -j8 -l8
[1.217s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/yuan/amr_ws/build/slpmu_power': CMAKE_PREFIX_PATH=/home/<USER>/yuan/amr_ws/install/slcan:/home/<USER>/yuan/amr_ws/install/slpmu_ros2:/home/<USER>/yuan/amr_ws/install/slpmu_process:/home/<USER>/yuan/amr_ws/install/slpmu_power:/home/<USER>/yuan/amr_ws/install/slpmu_motor:/home/<USER>/yuan/amr_ws/install/slpmu_jack:/opt/ros/humble PKG_CONFIG_PATH=/home/<USER>/yuan/amr_ws/install/slcan/lib/aarch64-linux-gnu/pkgconfig:/home/<USER>/yuan/amr_ws/install/slcan/lib/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_process/lib/aarch64-linux-gnu/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_process/lib/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_power/lib/aarch64-linux-gnu/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_power/lib/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_motor/lib/aarch64-linux-gnu/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_motor/lib/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_jack/lib/aarch64-linux-gnu/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_jack/lib/pkgconfig /usr/bin/cmake --install /home/<USER>/yuan/amr_ws/build/slpmu_power
[1.247s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/yuan/amr_ws/build/slpmu_jack' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/yuan/amr_ws/install/slcan:/home/<USER>/yuan/amr_ws/install/slpmu_ros2:/home/<USER>/yuan/amr_ws/install/slpmu_process:/home/<USER>/yuan/amr_ws/install/slpmu_power:/home/<USER>/yuan/amr_ws/install/slpmu_motor:/home/<USER>/yuan/amr_ws/install/slpmu_jack:/opt/ros/humble PKG_CONFIG_PATH=/home/<USER>/yuan/amr_ws/install/slcan/lib/aarch64-linux-gnu/pkgconfig:/home/<USER>/yuan/amr_ws/install/slcan/lib/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_process/lib/aarch64-linux-gnu/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_process/lib/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_power/lib/aarch64-linux-gnu/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_power/lib/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_motor/lib/aarch64-linux-gnu/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_motor/lib/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_jack/lib/aarch64-linux-gnu/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_jack/lib/pkgconfig /usr/bin/cmake --build /home/<USER>/yuan/amr_ws/build/slpmu_jack -- -j8 -l8
[1.255s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/yuan/amr_ws/build/slpmu_jack': CMAKE_PREFIX_PATH=/home/<USER>/yuan/amr_ws/install/slcan:/home/<USER>/yuan/amr_ws/install/slpmu_ros2:/home/<USER>/yuan/amr_ws/install/slpmu_process:/home/<USER>/yuan/amr_ws/install/slpmu_power:/home/<USER>/yuan/amr_ws/install/slpmu_motor:/home/<USER>/yuan/amr_ws/install/slpmu_jack:/opt/ros/humble PKG_CONFIG_PATH=/home/<USER>/yuan/amr_ws/install/slcan/lib/aarch64-linux-gnu/pkgconfig:/home/<USER>/yuan/amr_ws/install/slcan/lib/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_process/lib/aarch64-linux-gnu/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_process/lib/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_power/lib/aarch64-linux-gnu/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_power/lib/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_motor/lib/aarch64-linux-gnu/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_motor/lib/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_jack/lib/aarch64-linux-gnu/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_jack/lib/pkgconfig /usr/bin/cmake --install /home/<USER>/yuan/amr_ws/build/slpmu_jack
[1.263s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_power', 'pkg_config_path')
[1.265s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/yuan/amr_ws/install/slpmu_power/share/slpmu_power/hook/pkg_config_path.ps1'
[1.266s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/yuan/amr_ws/build/slpmu_power' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/yuan/amr_ws/install/slcan:/home/<USER>/yuan/amr_ws/install/slpmu_ros2:/home/<USER>/yuan/amr_ws/install/slpmu_process:/home/<USER>/yuan/amr_ws/install/slpmu_power:/home/<USER>/yuan/amr_ws/install/slpmu_motor:/home/<USER>/yuan/amr_ws/install/slpmu_jack:/opt/ros/humble PKG_CONFIG_PATH=/home/<USER>/yuan/amr_ws/install/slcan/lib/aarch64-linux-gnu/pkgconfig:/home/<USER>/yuan/amr_ws/install/slcan/lib/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_process/lib/aarch64-linux-gnu/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_process/lib/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_power/lib/aarch64-linux-gnu/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_power/lib/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_motor/lib/aarch64-linux-gnu/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_motor/lib/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_jack/lib/aarch64-linux-gnu/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_jack/lib/pkgconfig /usr/bin/cmake --install /home/<USER>/yuan/amr_ws/build/slpmu_power
[1.267s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/yuan/amr_ws/install/slpmu_power/share/slpmu_power/hook/pkg_config_path.dsv'
[1.268s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/yuan/amr_ws/install/slpmu_power/share/slpmu_power/hook/pkg_config_path.sh'
[1.272s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_power', 'pkg_config_path_multiarch')
[1.274s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/yuan/amr_ws/install/slpmu_power/share/slpmu_power/hook/pkg_config_path_multiarch.ps1'
[1.277s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/yuan/amr_ws/install/slpmu_power/share/slpmu_power/hook/pkg_config_path_multiarch.dsv'
[1.279s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/yuan/amr_ws/install/slpmu_power/share/slpmu_power/hook/pkg_config_path_multiarch.sh'
[1.281s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(slpmu_power)
[1.283s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/yuan/amr_ws/install/slpmu_power' for CMake module files
[1.285s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/yuan/amr_ws/install/slpmu_power' for CMake config files
[1.286s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_power', 'cmake_prefix_path')
[1.287s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/yuan/amr_ws/install/slpmu_power/share/slpmu_power/hook/cmake_prefix_path.ps1'
[1.290s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/yuan/amr_ws/install/slpmu_power/share/slpmu_power/hook/cmake_prefix_path.dsv'
[1.292s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/yuan/amr_ws/install/slpmu_power/share/slpmu_power/hook/cmake_prefix_path.sh'
[1.295s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/yuan/amr_ws/install/slpmu_power/lib'
[1.296s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/yuan/amr_ws/install/slpmu_power/bin'
[1.296s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_power', 'path')
[1.297s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/yuan/amr_ws/install/slpmu_power/share/slpmu_power/hook/path.ps1'
[1.300s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/yuan/amr_ws/install/slpmu_power/share/slpmu_power/hook/path.dsv'
[1.301s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/yuan/amr_ws/install/slpmu_power/share/slpmu_power/hook/path.sh'
[1.303s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/yuan/amr_ws/install/slpmu_power/lib/pkgconfig/slpmu_power.pc'
[1.304s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/yuan/amr_ws/install/slpmu_power/lib/python3.10/site-packages'
[1.304s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/yuan/amr_ws/install/slpmu_power/bin'
[1.304s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_power', 'pythonscriptspath')
[1.305s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/yuan/amr_ws/install/slpmu_power/share/slpmu_power/hook/pythonscriptspath.ps1'
[1.306s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/yuan/amr_ws/install/slpmu_power/share/slpmu_power/hook/pythonscriptspath.dsv'
[1.306s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/yuan/amr_ws/install/slpmu_power/share/slpmu_power/hook/pythonscriptspath.sh'
[1.310s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/yuan/amr_ws/install/slpmu_power/share/slpmu_power/package.ps1'
[1.312s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/yuan/amr_ws/install/slpmu_power/share/slpmu_power/package.dsv'
[1.313s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/yuan/amr_ws/install/slpmu_power/share/slpmu_power/package.sh'
[1.314s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/yuan/amr_ws/install/slpmu_power/share/slpmu_power/package.bash'
[1.315s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/yuan/amr_ws/install/slpmu_power/share/slpmu_power/package.zsh'
[1.315s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/yuan/amr_ws/install/slpmu_power/share/colcon-core/packages/slpmu_power)
[1.319s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_jack', 'pkg_config_path')
[1.319s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/yuan/amr_ws/install/slpmu_jack/share/slpmu_jack/hook/pkg_config_path.ps1'
[1.320s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/yuan/amr_ws/build/slpmu_jack' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/yuan/amr_ws/install/slcan:/home/<USER>/yuan/amr_ws/install/slpmu_ros2:/home/<USER>/yuan/amr_ws/install/slpmu_process:/home/<USER>/yuan/amr_ws/install/slpmu_power:/home/<USER>/yuan/amr_ws/install/slpmu_motor:/home/<USER>/yuan/amr_ws/install/slpmu_jack:/opt/ros/humble PKG_CONFIG_PATH=/home/<USER>/yuan/amr_ws/install/slcan/lib/aarch64-linux-gnu/pkgconfig:/home/<USER>/yuan/amr_ws/install/slcan/lib/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_process/lib/aarch64-linux-gnu/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_process/lib/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_power/lib/aarch64-linux-gnu/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_power/lib/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_motor/lib/aarch64-linux-gnu/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_motor/lib/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_jack/lib/aarch64-linux-gnu/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_jack/lib/pkgconfig /usr/bin/cmake --install /home/<USER>/yuan/amr_ws/build/slpmu_jack
[1.321s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/yuan/amr_ws/install/slpmu_jack/share/slpmu_jack/hook/pkg_config_path.dsv'
[1.322s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/yuan/amr_ws/install/slpmu_jack/share/slpmu_jack/hook/pkg_config_path.sh'
[1.323s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_jack', 'pkg_config_path_multiarch')
[1.324s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/yuan/amr_ws/install/slpmu_jack/share/slpmu_jack/hook/pkg_config_path_multiarch.ps1'
[1.325s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/yuan/amr_ws/install/slpmu_jack/share/slpmu_jack/hook/pkg_config_path_multiarch.dsv'
[1.326s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/yuan/amr_ws/install/slpmu_jack/share/slpmu_jack/hook/pkg_config_path_multiarch.sh'
[1.326s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(slpmu_jack)
[1.327s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/yuan/amr_ws/install/slpmu_jack' for CMake module files
[1.327s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/yuan/amr_ws/install/slpmu_jack' for CMake config files
[1.328s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_jack', 'cmake_prefix_path')
[1.328s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/yuan/amr_ws/install/slpmu_jack/share/slpmu_jack/hook/cmake_prefix_path.ps1'
[1.329s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/yuan/amr_ws/install/slpmu_jack/share/slpmu_jack/hook/cmake_prefix_path.dsv'
[1.330s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/yuan/amr_ws/install/slpmu_jack/share/slpmu_jack/hook/cmake_prefix_path.sh'
[1.331s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/yuan/amr_ws/install/slpmu_jack/lib'
[1.331s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/yuan/amr_ws/install/slpmu_jack/bin'
[1.331s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_jack', 'path')
[1.332s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/yuan/amr_ws/install/slpmu_jack/share/slpmu_jack/hook/path.ps1'
[1.333s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/yuan/amr_ws/install/slpmu_jack/share/slpmu_jack/hook/path.dsv'
[1.334s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/yuan/amr_ws/install/slpmu_jack/share/slpmu_jack/hook/path.sh'
[1.334s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/yuan/amr_ws/install/slpmu_jack/lib/pkgconfig/slpmu_jack.pc'
[1.335s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/yuan/amr_ws/install/slpmu_jack/lib/python3.10/site-packages'
[1.335s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/yuan/amr_ws/install/slpmu_jack/bin'
[1.335s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_jack', 'pythonscriptspath')
[1.336s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/yuan/amr_ws/install/slpmu_jack/share/slpmu_jack/hook/pythonscriptspath.ps1'
[1.337s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/yuan/amr_ws/install/slpmu_jack/share/slpmu_jack/hook/pythonscriptspath.dsv'
[1.337s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/yuan/amr_ws/install/slpmu_jack/share/slpmu_jack/hook/pythonscriptspath.sh'
[1.339s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/yuan/amr_ws/install/slpmu_jack/share/slpmu_jack/package.ps1'
[1.340s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/yuan/amr_ws/install/slpmu_jack/share/slpmu_jack/package.dsv'
[1.341s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/yuan/amr_ws/install/slpmu_jack/share/slpmu_jack/package.sh'
[1.342s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/yuan/amr_ws/install/slpmu_jack/share/slpmu_jack/package.bash'
[1.342s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/yuan/amr_ws/install/slpmu_jack/share/slpmu_jack/package.zsh'
[1.343s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/yuan/amr_ws/install/slpmu_jack/share/colcon-core/packages/slpmu_jack)
[3.396s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/yuan/amr_ws/build/slpmu_motor' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/yuan/amr_ws/install/slcan:/home/<USER>/yuan/amr_ws/install/slpmu_ros2:/home/<USER>/yuan/amr_ws/install/slpmu_process:/home/<USER>/yuan/amr_ws/install/slpmu_power:/home/<USER>/yuan/amr_ws/install/slpmu_motor:/home/<USER>/yuan/amr_ws/install/slpmu_jack:/opt/ros/humble PKG_CONFIG_PATH=/home/<USER>/yuan/amr_ws/install/slcan/lib/aarch64-linux-gnu/pkgconfig:/home/<USER>/yuan/amr_ws/install/slcan/lib/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_process/lib/aarch64-linux-gnu/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_process/lib/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_power/lib/aarch64-linux-gnu/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_power/lib/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_motor/lib/aarch64-linux-gnu/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_motor/lib/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_jack/lib/aarch64-linux-gnu/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_jack/lib/pkgconfig /usr/bin/cmake --build /home/<USER>/yuan/amr_ws/build/slpmu_motor -- -j8 -l8
[3.400s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/yuan/amr_ws/build/slpmu_motor': CMAKE_PREFIX_PATH=/home/<USER>/yuan/amr_ws/install/slcan:/home/<USER>/yuan/amr_ws/install/slpmu_ros2:/home/<USER>/yuan/amr_ws/install/slpmu_process:/home/<USER>/yuan/amr_ws/install/slpmu_power:/home/<USER>/yuan/amr_ws/install/slpmu_motor:/home/<USER>/yuan/amr_ws/install/slpmu_jack:/opt/ros/humble PKG_CONFIG_PATH=/home/<USER>/yuan/amr_ws/install/slcan/lib/aarch64-linux-gnu/pkgconfig:/home/<USER>/yuan/amr_ws/install/slcan/lib/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_process/lib/aarch64-linux-gnu/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_process/lib/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_power/lib/aarch64-linux-gnu/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_power/lib/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_motor/lib/aarch64-linux-gnu/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_motor/lib/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_jack/lib/aarch64-linux-gnu/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_jack/lib/pkgconfig /usr/bin/cmake --install /home/<USER>/yuan/amr_ws/build/slpmu_motor
[3.434s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/yuan/amr_ws/build/slpmu_motor' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/yuan/amr_ws/install/slcan:/home/<USER>/yuan/amr_ws/install/slpmu_ros2:/home/<USER>/yuan/amr_ws/install/slpmu_process:/home/<USER>/yuan/amr_ws/install/slpmu_power:/home/<USER>/yuan/amr_ws/install/slpmu_motor:/home/<USER>/yuan/amr_ws/install/slpmu_jack:/opt/ros/humble PKG_CONFIG_PATH=/home/<USER>/yuan/amr_ws/install/slcan/lib/aarch64-linux-gnu/pkgconfig:/home/<USER>/yuan/amr_ws/install/slcan/lib/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_process/lib/aarch64-linux-gnu/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_process/lib/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_power/lib/aarch64-linux-gnu/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_power/lib/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_motor/lib/aarch64-linux-gnu/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_motor/lib/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_jack/lib/aarch64-linux-gnu/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_jack/lib/pkgconfig /usr/bin/cmake --install /home/<USER>/yuan/amr_ws/build/slpmu_motor
[3.435s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_motor', 'pkg_config_path')
[3.437s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/yuan/amr_ws/install/slpmu_motor/share/slpmu_motor/hook/pkg_config_path.ps1'
[3.439s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/yuan/amr_ws/install/slpmu_motor/share/slpmu_motor/hook/pkg_config_path.dsv'
[3.441s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/yuan/amr_ws/install/slpmu_motor/share/slpmu_motor/hook/pkg_config_path.sh'
[3.444s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_motor', 'pkg_config_path_multiarch')
[3.446s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/yuan/amr_ws/install/slpmu_motor/share/slpmu_motor/hook/pkg_config_path_multiarch.ps1'
[3.447s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/yuan/amr_ws/install/slpmu_motor/share/slpmu_motor/hook/pkg_config_path_multiarch.dsv'
[3.449s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/yuan/amr_ws/install/slpmu_motor/share/slpmu_motor/hook/pkg_config_path_multiarch.sh'
[3.451s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(slpmu_motor)
[3.452s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/yuan/amr_ws/install/slpmu_motor' for CMake module files
[3.454s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/yuan/amr_ws/install/slpmu_motor' for CMake config files
[3.455s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_motor', 'cmake_prefix_path')
[3.457s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/yuan/amr_ws/install/slpmu_motor/share/slpmu_motor/hook/cmake_prefix_path.ps1'
[3.458s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/yuan/amr_ws/install/slpmu_motor/share/slpmu_motor/hook/cmake_prefix_path.dsv'
[3.460s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/yuan/amr_ws/install/slpmu_motor/share/slpmu_motor/hook/cmake_prefix_path.sh'
[3.462s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/yuan/amr_ws/install/slpmu_motor/lib'
[3.462s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/yuan/amr_ws/install/slpmu_motor/bin'
[3.463s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_motor', 'path')
[3.464s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/yuan/amr_ws/install/slpmu_motor/share/slpmu_motor/hook/path.ps1'
[3.465s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/yuan/amr_ws/install/slpmu_motor/share/slpmu_motor/hook/path.dsv'
[3.467s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/yuan/amr_ws/install/slpmu_motor/share/slpmu_motor/hook/path.sh'
[3.468s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/yuan/amr_ws/install/slpmu_motor/lib/pkgconfig/slpmu_motor.pc'
[3.470s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/yuan/amr_ws/install/slpmu_motor/lib/python3.10/site-packages'
[3.471s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/yuan/amr_ws/install/slpmu_motor/bin'
[3.471s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_motor', 'pythonscriptspath')
[3.472s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/yuan/amr_ws/install/slpmu_motor/share/slpmu_motor/hook/pythonscriptspath.ps1'
[3.474s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/yuan/amr_ws/install/slpmu_motor/share/slpmu_motor/hook/pythonscriptspath.dsv'
[3.475s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/yuan/amr_ws/install/slpmu_motor/share/slpmu_motor/hook/pythonscriptspath.sh'
[3.480s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/yuan/amr_ws/install/slpmu_motor/share/slpmu_motor/package.ps1'
[3.483s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/yuan/amr_ws/install/slpmu_motor/share/slpmu_motor/package.dsv'
[3.484s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/yuan/amr_ws/install/slpmu_motor/share/slpmu_motor/package.sh'
[3.484s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/yuan/amr_ws/install/slpmu_motor/share/slpmu_motor/package.bash'
[3.485s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/yuan/amr_ws/install/slpmu_motor/share/slpmu_motor/package.zsh'
[3.485s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/yuan/amr_ws/install/slpmu_motor/share/colcon-core/packages/slpmu_motor)
[3.486s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/yuan/amr_ws/src/pmu/slpmu_ros2' with build type 'ament_cmake'
[3.486s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/yuan/amr_ws/src/pmu/slpmu_ros2'
[3.487s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[3.487s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[3.511s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/yuan/amr_ws/build/slpmu_ros2': CMAKE_PREFIX_PATH=/home/<USER>/yuan/amr_ws/install/slpmu_motor:/home/<USER>/yuan/amr_ws/install/slpmu_jack:/home/<USER>/yuan/amr_ws/install/slpmu_process:/home/<USER>/yuan/amr_ws/install/slcan:/home/<USER>/yuan/amr_ws/install/slpmu_ros2:/home/<USER>/yuan/amr_ws/install/slpmu_power:/opt/ros/humble PATH=/home/<USER>/yuan/amr_ws/install/slpmu_motor/bin:/home/<USER>/yuan/amr_ws/install/slpmu_jack/bin:/home/<USER>/yuan/amr_ws/install/slpmu_power/bin:/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin PKG_CONFIG_PATH=/home/<USER>/yuan/amr_ws/install/slpmu_motor/lib/aarch64-linux-gnu/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_motor/lib/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_jack/lib/aarch64-linux-gnu/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_jack/lib/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_process/lib/aarch64-linux-gnu/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_process/lib/pkgconfig:/home/<USER>/yuan/amr_ws/install/slcan/lib/aarch64-linux-gnu/pkgconfig:/home/<USER>/yuan/amr_ws/install/slcan/lib/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_power/lib/aarch64-linux-gnu/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_power/lib/pkgconfig /usr/bin/cmake --build /home/<USER>/yuan/amr_ws/build/slpmu_ros2 -- -j8 -l8
[38.812s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/yuan/amr_ws/build/slpmu_ros2' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/yuan/amr_ws/install/slpmu_motor:/home/<USER>/yuan/amr_ws/install/slpmu_jack:/home/<USER>/yuan/amr_ws/install/slpmu_process:/home/<USER>/yuan/amr_ws/install/slcan:/home/<USER>/yuan/amr_ws/install/slpmu_ros2:/home/<USER>/yuan/amr_ws/install/slpmu_power:/opt/ros/humble PATH=/home/<USER>/yuan/amr_ws/install/slpmu_motor/bin:/home/<USER>/yuan/amr_ws/install/slpmu_jack/bin:/home/<USER>/yuan/amr_ws/install/slpmu_power/bin:/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin PKG_CONFIG_PATH=/home/<USER>/yuan/amr_ws/install/slpmu_motor/lib/aarch64-linux-gnu/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_motor/lib/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_jack/lib/aarch64-linux-gnu/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_jack/lib/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_process/lib/aarch64-linux-gnu/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_process/lib/pkgconfig:/home/<USER>/yuan/amr_ws/install/slcan/lib/aarch64-linux-gnu/pkgconfig:/home/<USER>/yuan/amr_ws/install/slcan/lib/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_power/lib/aarch64-linux-gnu/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_power/lib/pkgconfig /usr/bin/cmake --build /home/<USER>/yuan/amr_ws/build/slpmu_ros2 -- -j8 -l8
[38.819s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/yuan/amr_ws/build/slpmu_ros2': CMAKE_PREFIX_PATH=/home/<USER>/yuan/amr_ws/install/slpmu_motor:/home/<USER>/yuan/amr_ws/install/slpmu_jack:/home/<USER>/yuan/amr_ws/install/slpmu_process:/home/<USER>/yuan/amr_ws/install/slcan:/home/<USER>/yuan/amr_ws/install/slpmu_ros2:/home/<USER>/yuan/amr_ws/install/slpmu_power:/opt/ros/humble PATH=/home/<USER>/yuan/amr_ws/install/slpmu_motor/bin:/home/<USER>/yuan/amr_ws/install/slpmu_jack/bin:/home/<USER>/yuan/amr_ws/install/slpmu_power/bin:/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin PKG_CONFIG_PATH=/home/<USER>/yuan/amr_ws/install/slpmu_motor/lib/aarch64-linux-gnu/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_motor/lib/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_jack/lib/aarch64-linux-gnu/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_jack/lib/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_process/lib/aarch64-linux-gnu/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_process/lib/pkgconfig:/home/<USER>/yuan/amr_ws/install/slcan/lib/aarch64-linux-gnu/pkgconfig:/home/<USER>/yuan/amr_ws/install/slcan/lib/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_power/lib/aarch64-linux-gnu/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_power/lib/pkgconfig /usr/bin/cmake --install /home/<USER>/yuan/amr_ws/build/slpmu_ros2
[38.984s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(slpmu_ros2)
[38.986s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/yuan/amr_ws/install/slpmu_ros2' for CMake module files
[38.987s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/yuan/amr_ws/build/slpmu_ros2' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/yuan/amr_ws/install/slpmu_motor:/home/<USER>/yuan/amr_ws/install/slpmu_jack:/home/<USER>/yuan/amr_ws/install/slpmu_process:/home/<USER>/yuan/amr_ws/install/slcan:/home/<USER>/yuan/amr_ws/install/slpmu_ros2:/home/<USER>/yuan/amr_ws/install/slpmu_power:/opt/ros/humble PATH=/home/<USER>/yuan/amr_ws/install/slpmu_motor/bin:/home/<USER>/yuan/amr_ws/install/slpmu_jack/bin:/home/<USER>/yuan/amr_ws/install/slpmu_power/bin:/opt/ros/humble/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin PKG_CONFIG_PATH=/home/<USER>/yuan/amr_ws/install/slpmu_motor/lib/aarch64-linux-gnu/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_motor/lib/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_jack/lib/aarch64-linux-gnu/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_jack/lib/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_process/lib/aarch64-linux-gnu/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_process/lib/pkgconfig:/home/<USER>/yuan/amr_ws/install/slcan/lib/aarch64-linux-gnu/pkgconfig:/home/<USER>/yuan/amr_ws/install/slcan/lib/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_power/lib/aarch64-linux-gnu/pkgconfig:/home/<USER>/yuan/amr_ws/install/slpmu_power/lib/pkgconfig /usr/bin/cmake --install /home/<USER>/yuan/amr_ws/build/slpmu_ros2
[38.990s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/yuan/amr_ws/install/slpmu_ros2' for CMake config files
[38.993s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_ros2', 'cmake_prefix_path')
[38.994s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/yuan/amr_ws/install/slpmu_ros2/share/slpmu_ros2/hook/cmake_prefix_path.ps1'
[39.003s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/yuan/amr_ws/install/slpmu_ros2/share/slpmu_ros2/hook/cmake_prefix_path.dsv'
[39.005s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/yuan/amr_ws/install/slpmu_ros2/share/slpmu_ros2/hook/cmake_prefix_path.sh'
[39.008s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/yuan/amr_ws/install/slpmu_ros2/lib'
[39.008s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_ros2', 'ld_library_path_lib')
[39.010s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/yuan/amr_ws/install/slpmu_ros2/share/slpmu_ros2/hook/ld_library_path_lib.ps1'
[39.012s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/yuan/amr_ws/install/slpmu_ros2/share/slpmu_ros2/hook/ld_library_path_lib.dsv'
[39.013s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/yuan/amr_ws/install/slpmu_ros2/share/slpmu_ros2/hook/ld_library_path_lib.sh'
[39.015s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/yuan/amr_ws/install/slpmu_ros2/bin'
[39.015s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/yuan/amr_ws/install/slpmu_ros2/lib/pkgconfig/slpmu_ros2.pc'
[39.016s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/yuan/amr_ws/install/slpmu_ros2/lib/python3.10/site-packages'
[39.018s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/yuan/amr_ws/install/slpmu_ros2/bin'
[39.020s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/yuan/amr_ws/install/slpmu_ros2/share/slpmu_ros2/package.ps1'
[39.022s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/yuan/amr_ws/install/slpmu_ros2/share/slpmu_ros2/package.dsv'
[39.024s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/yuan/amr_ws/install/slpmu_ros2/share/slpmu_ros2/package.sh'
[39.027s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/yuan/amr_ws/install/slpmu_ros2/share/slpmu_ros2/package.bash'
[39.029s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/yuan/amr_ws/install/slpmu_ros2/share/slpmu_ros2/package.zsh'
[39.032s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/yuan/amr_ws/install/slpmu_ros2/share/colcon-core/packages/slpmu_ros2)
[39.036s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(slpmu_ros2)
[39.038s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/yuan/amr_ws/install/slpmu_ros2' for CMake module files
[39.040s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/yuan/amr_ws/install/slpmu_ros2' for CMake config files
[39.041s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_ros2', 'cmake_prefix_path')
[39.042s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/yuan/amr_ws/install/slpmu_ros2/share/slpmu_ros2/hook/cmake_prefix_path.ps1'
[39.049s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/yuan/amr_ws/install/slpmu_ros2/share/slpmu_ros2/hook/cmake_prefix_path.dsv'
[39.050s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/yuan/amr_ws/install/slpmu_ros2/share/slpmu_ros2/hook/cmake_prefix_path.sh'
[39.054s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/yuan/amr_ws/install/slpmu_ros2/lib'
[39.055s] Level 1:colcon.colcon_core.shell:create_environment_hook('slpmu_ros2', 'ld_library_path_lib')
[39.055s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/yuan/amr_ws/install/slpmu_ros2/share/slpmu_ros2/hook/ld_library_path_lib.ps1'
[39.057s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/yuan/amr_ws/install/slpmu_ros2/share/slpmu_ros2/hook/ld_library_path_lib.dsv'
[39.058s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/yuan/amr_ws/install/slpmu_ros2/share/slpmu_ros2/hook/ld_library_path_lib.sh'
[39.058s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/yuan/amr_ws/install/slpmu_ros2/bin'
[39.059s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/yuan/amr_ws/install/slpmu_ros2/lib/pkgconfig/slpmu_ros2.pc'
[39.059s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/yuan/amr_ws/install/slpmu_ros2/lib/python3.10/site-packages'
[39.059s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/yuan/amr_ws/install/slpmu_ros2/bin'
[39.060s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/yuan/amr_ws/install/slpmu_ros2/share/slpmu_ros2/package.ps1'
[39.061s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/yuan/amr_ws/install/slpmu_ros2/share/slpmu_ros2/package.dsv'
[39.062s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/yuan/amr_ws/install/slpmu_ros2/share/slpmu_ros2/package.sh'
[39.062s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/yuan/amr_ws/install/slpmu_ros2/share/slpmu_ros2/package.bash'
[39.063s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/yuan/amr_ws/install/slpmu_ros2/share/slpmu_ros2/package.zsh'
[39.064s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/yuan/amr_ws/install/slpmu_ros2/share/colcon-core/packages/slpmu_ros2)
[39.064s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[39.066s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[39.066s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[39.066s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[39.074s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[39.075s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[39.075s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[64.100s] DEBUG:colcon.colcon_notification.desktop_notification.notify2:Failed to initialize notify2: org.freedesktop.DBus.Error.NoReply: Did not receive a reply. Possible causes include: the remote application did not send a reply, the message bus security policy blocked the reply, the reply timeout expired, or the network connection was broken.
[64.101s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[64.101s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/yuan/amr_ws/install/local_setup.ps1'
[64.103s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/yuan/amr_ws/install/_local_setup_util_ps1.py'
[64.105s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/yuan/amr_ws/install/setup.ps1'
[64.107s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/yuan/amr_ws/install/local_setup.sh'
[64.107s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/yuan/amr_ws/install/_local_setup_util_sh.py'
[64.108s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/yuan/amr_ws/install/setup.sh'
[64.109s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/yuan/amr_ws/install/local_setup.bash'
[64.110s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/yuan/amr_ws/install/setup.bash'
[64.111s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/yuan/amr_ws/install/local_setup.zsh'
[64.112s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/yuan/amr_ws/install/setup.zsh'
